'use strict';

module.exports = {
  // 单行输出（不折行）的（最大）长度
  printWidth: 100,
  // 每一个水平缩进的空格数
  tabWidth: 2,
  // 在语句末尾添加分号;
  semi: true,
  // 使用单引号而非双引号
  singleQuote: true,
  // 在任何可能的多行中输入尾逗号
  trailingComma: 'es5',
  // 在对象字面量声明所使用的的花括号后（{）和前（}）输出空格
  bracketSpacing: true,
  // 为单行箭头函数的参数添加圆括号。
  arrowParens: 'always',
  // 禁用换行
  overrides: [
    {
      files: '*.scss',
      options: {
        trailingComma: 'none',
        singleQuote: false,
        arrowParens: 'avoid',
        parser: 'css',
      },
    },
  ],
};
