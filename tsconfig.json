{
  "compileOnSave": false,
  "buildOnSave": false,
  "compilerOptions": {
    "baseUrl": ".",
    "outDir": "build",
    "module": "esnext",
    "target": "es5",
    "jsx": "react",
    "moduleResolution": "node",
    "allowSyntheticDefaultImports": true,
    "lib": ["esnext", "dom"],
    "sourceMap": true,
    "allowJs": false,
    "rootDirs": ["src", "development"],
    "forceConsistentCasingInFileNames": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noImplicitAny": false,
    "importHelpers": false,
    "strictNullChecks": true,
    "suppressImplicitAnyIndexErrors": true,
    "skipLibCheck": true,
    "paths": {
      "@/*": [
        "./src/*"
      ],
    }
  },
  "include": ["src/**/*", "development/**/*"],
  "exclude": ["node_modules", "build", "public"]
}
