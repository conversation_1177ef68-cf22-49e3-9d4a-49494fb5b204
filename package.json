{"name": "@ali/assets-byf-service", "version": "0.0.1", "description": "消费者体验提升计划项目，用以提升消费者和商家的体验。", "author": "初谦", "scripts": {"start": "material-scripts start merchant", "lint": "mad lint", "eslint": "eslint --cache --ext .js,.jsx,.ts,.tsx ./", "eslint:fix": "npm run eslint -- --fix", "stylelint": "stylelint \"**/*.{css,scss,less}\"", "precommit": "mad lint --staged", "postcommit": "mad lint --postcommit"}, "dependencies": {"@ali/iec-dtao-utils": "0.0.8", "@ali/merchant-platform-common-utils": "^1.6.3", "@ali/mkt-design": "1.2.10", "@alifd/next": "^1.25.49", "@alife/iec-dtao-delivery-pc": "0.0.3", "@types/react": "^16.9.56", "@types/react-router-dom": "^5.1.7", "dayjs": "^1.11.12", "lodash-es": "^4.17.21", "moment": "2.27.0", "numeral": "^2.0.6", "react": "17.0.1", "react-dom": "17.0.1", "react-router-dom": "5.2.0", "typescript": "^4.3.5"}, "devDependencies": {"@ali/eslint-config-fin": "latest", "@ali/fin-lint": "^1.1.11", "eslint": "^6.8.0", "husky": "^0.14.3", "stylelint": "^13.3.2"}, "repository": "http://gitlab.alibaba-inc.com/merchant-micro-apps/assets-byf-service.git"}