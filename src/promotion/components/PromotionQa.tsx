import React from 'react';
import styles from './styles.module.scss';
import ExclusiveLogoPanel from '@/components/ExclusiveLogoPanel';

const qaList = {
  Q1: {
    title: <>Q：哪些大促会生效退货宝？</>,
    content: <>A：退货宝产品页会展示您选择生效退货宝的活动，具体请以招商规则为准。</>,
  },
  Q2: {
    title: <>Q：大促生效退货宝规则？</>,
    content: <>A：生效时间：活动期间生效，如您参加活动时间晚于活动开始时间，则从报名成功后开始生效，活动结束后自动失效；生效范围：预售阶段，活动商品生效；现货阶段，全店商品生效，部分类目除外，具体以招商规则为准。</>,
  },
  Q3: {
    title: <>Q：退出大促后退货宝还会继续生效吗？</>,
    content: <>A：退出某个大促活动后，您将不会再因为该活动生效退货宝。</>,
  },
  Q4: {
    title: <>Q：退出消费者体验提升计划（含退货宝权益）会影响大促报名吗？</>,
    content: <>A：不会。</>,
  },
  Q5: {
    title: <>Q：怎么退出消费者体验提升计划（含退货宝权益）？</>,
    content: <>A：通过大促活动的报名流程选择退出，具体以活动要求为准。</>,
  },
  Q6: {
    title: <>Q：为什么没办法退出消费者体验提升计划（含退货宝权益）？</>,
    content:
  <>A：抱歉，可能是因为您不满足大促活动退出消费者体验提升计划的条件，请您前往招商规则查看详情，具体的退出失败原因请以大促活动页面提示为准，如有疑问可咨询官方客服。</>,
  },
  Q7: {
    title: <>Q：为什么有些订单没法享受退货宝权益？</>,
    content: <>A：部分类目大促不要求生效，您可以选择升级为长期生效，具体请以实际出单为准。</>,
  },
  Q8: {
    title: <>Q：退货宝服务保障90天是什么意思？90天内消费者都可以退换货吗？</>,
    content: <>A：90天保障期是行业退货包运费服务的通用标准。退货宝并不影响商品现有的退换货规则。每笔订单的退换货时效依据三包法及新消法的相关规定、《淘宝网七天无理由退货规范》、《淘宝平台争议处理规则》等相关规则，以及买卖双方协商/卖家承诺。</>,
  },
  Q9: {
    title: <>Q：退货宝专属标识是怎么展示的？</>,
    content: <>A：退货宝专属标识的展示如下：</>,
    logoPanel: true,
  },
  Q10: {
    title: <>Q：消费者服务体验提升计划服务费价格是怎么确定的？</>,
    content: <>A：服务费用将基于近期店铺经营类目及规模、交易及售后等环节（如退货退款）的服务体验综合确定，并可能基于商家经营情况的变化进行调整。</>,
  },
  Q11: {
    title: <>Q：服务费是怎么收取的？</>,
    content: <>A：服务费按单收取，每笔实物订单在线确认发货后，系统自动从卖家支付宝账户扣除服务费；如未发货，则不收取服务费。</>,
  },
  Q12: {
    title: <>Q：消费者会收到多少元的运费保障？</>,
    content: <>A：会根据消费者和商家之间的地址进行测算，通常是按照两地间物流首重费用确定保障金额，最终保障的金额以实际抵扣或到账的金额为准。</>,
  },
  Q13: {
    title: <>Q：如何查看单个订单是否可享退货宝权益？</>,
    content: <>A：登录千牛---点击【交易】---查看【已卖出宝贝】---点击【保障详情】。订单显示“保障中”的状态，则该订单可享退货宝权益。</>,
  },
  Q14: {
    title: <>Q：退货宝能保障几次？</>,
    content: <>A：每笔订单（主订单/父订单维度，非子订单维度）仅可享受一次退货宝权益保障。</>,
  },
};

interface PromotionQaProps {
  noTitle?: boolean;
  isLogoOverflow?: boolean;
}

const PromotionQa = (props: PromotionQaProps) => {
  const { noTitle, isLogoOverflow } = props;
  return (
    <div className={styles.qa}>
      {
        !noTitle && <div className={styles['qa-title']}>常见问题</div>
      }
      <div className={styles['qa-content']}>
        {
          Object.keys(qaList).map((item) => {
            return (
              <div className={styles['qa-content-item']}>
                <div className={styles['qa-content-item-q']}>{qaList[item].title}</div>
                <div className={styles['qa-content-item-a']}>{qaList[item].content}</div>
                {qaList[item]?.logoPanel ? (
                  <div className={styles['qa-content-item-extra']}>
                    <ExclusiveLogoPanel isOverflow={isLogoOverflow} />
                  </div>
                ) : null}
              </div>
            );
          })
        }
      </div>
    </div>
  );
};

export default PromotionQa;
