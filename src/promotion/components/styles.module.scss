.fee-daily-base {
  display: flex;
  flex-direction: column;
  align-content: center;
  margin-right: 42px;
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  color: #111;
  .fee-daily-base-title {
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 14px;
    font-weight: 600;
    line-height: 14px;
    color: #111;
    .fee-daily-base-title-market {
      font-size: 12px;
      margin-left: 6px;
      color: #ff5000;
      padding: 3px 9px;
      line-height: 18px;
      width: auto;
      height: 24px;
      border-radius: 6px;
      background-color: #ffede5;
    }
  }
  .pay-info {
    display: flex;
    flex-direction: row;
    font-size: 14px;
    line-height: 30px;
    letter-spacing: 0;
    color: #666;
    .pay-normal-icon {
      margin-top: 10px;
      margin-left: 2px;
      width: 12px;
      height: 12px;
      background-image: url("https://img.alicdn.com/imgextra/i4/O1CN01WOM9TZ1b5f8zdsAry_!!6000000003414-2-tps-24-24.png");
      background-size: 12px 12px;
      background-repeat: no-repeat;
    }
    .pay-alipay-icon {
      margin-top: 8px;
      margin-right: 2px;
      width: 14px;
      height: 14px;
      background-image: url("https://img.alicdn.com/imgextra/i3/O1CN01F6taE01POeAfesm0x_!!6000000001831-2-tps-28-28.png");
      background-size: 14px 14px;
      background-repeat: no-repeat;
    }
  }
}
.fee-daily-half-content {
  display: flex;
  flex-direction: row;
  .fee-daily-half-content-main {
    .fee-daily-half-promotion {
      display: flex;
      flex-direction: row;
      .fee-daily-half-promotion-content {
        font-size: 14px;
        font-weight: 500;
        color: #111;
        span {
          line-height: 36px;
          font-family: "AlibabaFontMd";
          font-size: 30px;
        }
      }
    }
  }
  .fee-daily-half-promotion-info {
    display: flex;
    width: 284px;
    height: 60px;
    .fee-daily-half-promotion-info-title {
      font-size: 12px;
      color: #111;
    }
  }
}
.fee-daily-base-promotion {
  display: flex;
  align-items: flex-end;
  margin-top: 6px;
  .fee-daily-base-promotion-discount {
    font-size: 14px;
    font-weight: 500;
    color: #111;
    span {
      line-height: 36px;
      font-family: "AlibabaFontMd";
      font-size: 30px;
    }
  }
  .fee-daily-base-promotion-discount-origin {
    display: flex;
    flex-direction: row;
    color: #999;
    font-size: 14px;
    margin: 0 6px;
    text-decoration: line-through;
    text-decoration-color: rgba(153, 153, 153, 0.5);
  }
  .fee-daily-base-promotion-discount-tag {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 94px;
    height: 24px;
    align-items: center;
    color: #ff8000;
    font-weight: normal;
    font-size: 14px;
    padding: 2px;
    border-radius: 6px;
    background: rgba(255, 128, 0, 0.06);

    .promotion-icon {
      margin-left: 2px;
      width: 14px;
      height: 14px;
      background-image: url("https://gw.alicdn.com/imgextra/i3/O1CN01btxTIO1LS4gN5DjCk_!!6000000001297-2-tps-24-24.png");
      background-size: 14px 14px;
      background-repeat: no-repeat;
    }
  }
}
.fee-daily-base-un-promotion {
  margin-top: 6px;
  font-size: 14px;
  font-weight: 500;
  line-height: 100%;
  letter-spacing: 0;
  color: #111;
  span {
    line-height: 36px;
    font-family: "AlibabaFontMd";
    font-size: 30px;
  }
}

.promotion-activities {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 141px;
  background-color: rgba(255, 168, 61, 0.06);
  border-radius: 12px;
  padding: 12px;

  .activities-menu {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
    height: 32px;

    .menu-header {
      display: flex;
      flex-direction: row;
      align-items: center;
      .menu-header-logo-opened {
        background-image: url("https://img.alicdn.com/imgextra/i3/O1CN01Y8bj2u1yASSDcUWHC_!!6000000006538-2-tps-285-31.png");
        background-size: 142px 16px;
        width: 142px;
        height: 16px;
        background-repeat: no-repeat;
        margin-right: 16px;
      }
      .menu-header-point {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;

        .menu-header-point-opened-icon {
          background-image: url("https://img.alicdn.com/imgextra/i4/O1CN01EcKo0Q1oskbEjfnDx_!!6000000005281-2-tps-28-28.png");
          width: 14px;
          height: 14px;
          background-size: 14px 14px;
          background-repeat: no-repeat;
          margin-right: 3px;
        }
        .menu-header-point-opened-desc {
          font-size: 14px;
          font-weight: normal;
          line-height: 20px;
          margin-right: 18px;
          color: #666;
          white-space: nowrap;
        }
      }
    }
    .menu-setting {
      display: flex;
      flex-direction: row;
      .menu-setting-quit {
        cursor: pointer;
        margin-right: 24px;
        font-size: 12px;
        color: #666;
      }
    }
  }
  .activities-desc {
    font-size: 12px;
    font-weight: normal;
    line-height: 18px;
    color: #999;
    margin-bottom: 3px;
  }
  .activities-panel {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    .activities-panel-content {
      display: flex;
      flex-direction: row;
      width: 960px;
      justify-content: space-between;
      .activities-panel-content-main {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        gap: 12px;
      }
      .activities-panel-content-button {
        width: 68px;
        height: 63px;
        border-radius: 12px;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        padding: 0 12px;
        border: 1px solid #d8d8d8;
        color: #999;
        cursor: pointer;

        .btn-view-all {
          width: 24px;
          height: 30px;
          // margin-right: 6px;
        }
        .btn-view-down {
          width: 14px;
          height: 14px;
          background-image: url("https://img.alicdn.com/imgextra/i4/O1CN01xvaKn91dgQDlRVriN_!!6000000003765-2-tps-28-28.png");
          background-size: 14px 14px;
        }
        .btn-view-up {
          width: 14px;
          height: 14px;
          background-image: url("https://img.alicdn.com/imgextra/i2/O1CN01zJCino1U2GxX7bpgH_!!6000000002459-2-tps-28-28.png");
          background-size: 14px 14px;
        }
      }
    }
  }
}

.item-panel {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 12px;
  padding: 12px;
  width: 285px;
  height: 63px;
  font-size: 12px;
  gap: 6px;

  .item-panel-title {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    .item-panel-title-text {
      font-weight: 600;
    }
    .item-panel-title-button {
      color: #999;
      font-size: 12px;
    }
    .item-panel-title-icon {
      width: 14px;
      height: 14px;
      background-image: url("https://img.alicdn.com/imgextra/i4/O1CN01ZBEaLI1qcNjTHrF84_!!6000000005516-2-tps-28-28.png");
      background-size: 14px 14px;
    }
  }
  .item-panel-content {
    display: flex;
    flex-direction: row;
    span {
      color: #666;
      font-family: "AlibabaNumberFontRg";
    }
  }
}

.activities-category {
  display: flex;
  flex-direction: column;
  margin-bottom: 10px;
  .content {
    display: flex;
    flex-direction: row;
    background-color: #f7f8fa;
    padding: 12px;
    align-items: flex-start;
    border-radius: 12px;
    .content-title {
      font-size: 14px;
      font-weight: 600;
      line-height: 20px;
      color: #111;
      width: 108px;
    }
    .content-desc {
      width: 346px;
      font-size: 12px;
      font-weight: normal;
      line-height: 18px;
      color: #999;
      word-wrap: break-word;
    }
  }
}

.quit-title {
  display: flex;
  flex-direction: row;
  font-size: 16px;
  line-height: 24px;
  color: #111;
  .quit-title-icon {
    width: 24px;
    height: 24px;
    margin-right: 8px;
    background-size: 24px 24px;
    background-image: url("https://img.alicdn.com/imgextra/i1/O1CN01THJWul1qyp8mXq5ox_!!6000000005565-2-tps-48-48.png");
  }
}
.quit-content {
  padding: 6px 32px;
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  color: #666;
}

.qa {
  display: flex;
  flex-direction: column;
  .qa-title {
    display: flex;
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    color: #111;
    margin-bottom: 24px;
    text-align: center;
  }
  .qa-content {
    display: flex;
    flex-direction: column;
    .qa-content-item {
      display: flex;
      flex-direction: column;
      margin-bottom: 18px;
      .qa-content-item-q {
        font-size: 12px;
        font-weight: 600;
        line-height: 24px;
        color: #111;
      }
      .qa-content-item-a {
        font-size: 12px;
        font-weight: normal;
        line-height: 18px;
        letter-spacing: 0;
        color: #666;
      }
    }
  }
}