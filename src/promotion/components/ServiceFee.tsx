/**
* @file ServiceFee
* @date 2025-02-12
* <AUTHOR>
*/
import React from 'react';
import BalloonContent from '@/components/BalloonContent';
import { Balloon, Icon } from '@alifd/next';
import { money_US } from '@ali/iec-dtao-utils/esm/common/number';
import styles from './styles.module.scss';


interface ServiceFeeProps {
  originalFeeAmount?: string;
  discountFeeAmount?: string;
  promotionFlag?: boolean;
  marketTitle?: string;
  alipayLoginId?: string | null;
}

const renderPromotionContent = ({
  promotionFlag,
  discountFeeAmount,
  originalFeeAmount,
  marketTitle,
}: {
  promotionFlag?: boolean;
  discountFeeAmount?: string;
  originalFeeAmount?: string;
  marketTitle?: string;
}) => {
  if (promotionFlag) {
    return (
      <>
        <div className={styles['fee-daily-base-title']}>
          预估服务费
          <BalloonContent
            icon={<Icon type="help" style={{ color: '#999' }} />}
            align="t"
            contentChildren={'当前服务费为预估服务费，费用按单收取，我们将根据您店铺交易订单及退换货情况每日动态调整服务费，最终服务费以出单为准'}
          />
          {
            marketTitle && (
              <div className={styles['fee-daily-base-title-market']}>
                {marketTitle}
              </div>
            )
          }
        </div>
        <div className={styles['fee-daily-base-promotion']}>
          <div className={styles['fee-daily-base-promotion-discount']}>
            <span>¥{money_US(discountFeeAmount) || '--'}</span> /单
          </div>
          <div className={styles['fee-daily-base-promotion-discount-origin']}>
            ¥{money_US(originalFeeAmount) || '--'}/单
            <BalloonContent
              icon={<Icon type="help" />}
              align="tl"
              contentWidth={120}
              contentChildren={'该价格为优惠前的价格'}
            />
          </div>
        </div>
      </>
    );
  }
  return (
    <>
      <div className={styles['fee-daily-base-title']}>
        当前服务费
        <BalloonContent
          icon={<Icon type="help" style={{ color: '#999' }} />}
          align="t"
          contentChildren={'当前服务费为预估服务费，费用按单收取，我们将根据您店铺交易订单及退换货情况每日动态调整服务费，最终服务费以出单为准'}
        />
      </div>
      <div className={styles['fee-daily-base-un-promotion']}>
        <span>¥{money_US(originalFeeAmount) || '--'}</span> /单
      </div>
    </>
  );
};

const ServiceFee = (props: ServiceFeeProps) => {
  const {
    promotionFlag,
    discountFeeAmount,
    originalFeeAmount,
    marketTitle,
    alipayLoginId,
  } = props;

  return (
    <>
      <div className={styles['fee-daily-base']}>
        {renderPromotionContent({
          promotionFlag,
          discountFeeAmount,
          originalFeeAmount,
          marketTitle,
        })}
        <div className={styles['pay-info']}>
          服务费扣款账户：
          <div className={styles['pay-alipay-icon']} />
          {alipayLoginId || '--'}
          <Balloon
            v2
            trigger={<div className={styles['pay-normal-icon']} />}
            align="r"
            triggerType="hover"
            closable={false}
          >
            <div>请确保账户资金充足，避免服务失效，从而导致客诉</div>
          </Balloon>
        </div>
      </div>
    </>
  );
};

export default ServiceFee;
