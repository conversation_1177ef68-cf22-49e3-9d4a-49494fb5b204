/**
* @file PromotionActivities
* @date 2025-02-12
* <AUTHOR>
*/

import React, { useEffect, useState } from 'react';
import styles from './styles.module.scss';
import { Button, Dialog, Drawer, Dropdown, Menu } from '@alifd/next';
import dayjs from 'dayjs';
import { log } from '@ali/iec-dtao-utils';
import PromotionQa from './PromotionQa';
import { includes } from 'lodash-es';
import { PROTOCOL_URL } from '@/constant';

interface PromotionActivitiesProps {
  status: string | null;
  beginTime: string | number;
  endTime: string | number;
  activityName: string;
  notSupportCategoryNames: string[];
}

interface PromotionActivitiesItemProps extends PromotionActivitiesProps {
  showActivityCategory: (params: { show: boolean; content: string[] }) => void;
}

const POINT_LIST = ['提升到店流量', '提升订单转化', '降低售后纠纷', '享退货宝专属标识'];
const STATUS_INFO = {
  NOT_EFFECTIVE: {
    title: '待生效',
    color: '#3D5EFF',
  },
  GUARANTEE: {
    title: '保障中',
    color: '#31CC31',
  },
};

const ActivitiesItem = ({
  status,
  beginTime,
  endTime,
  activityName,
  notSupportCategoryNames,
  showActivityCategory,
}: PromotionActivitiesItemProps) => {
  return (
    <div className={styles['item-panel']}>
      <div className={styles['item-panel-title']}>
        <div className={styles['item-panel-title-text']}>{activityName}</div>
        <Button onClick={() => { showActivityCategory({ show: true, content: notSupportCategoryNames }); }} text>
          <span className={styles['item-panel-title-button']}>查看类目</span>
          <div className={styles['item-panel-title-icon']} />
        </Button>
      </div>
      <div className={styles['item-panel-content']}>
        {status && <span style={{ color: `${STATUS_INFO[status]?.color}` }}>{STATUS_INFO[status]?.title}</span>}
        <span style={{ marginLeft: 4 }}>{beginTime ? dayjs(beginTime).format('YYYY-MM-DD HH:mm:ss') : '-'}~{endTime ? dayjs(endTime).format('YYYY-MM-DD HH:mm:ss') : '-'}</span>
      </div>
    </div>
  );
};

const PromotionActivities = ({
  activitiesData,
  onOpen,
  status,
}: {
  activitiesData: any[];
  onOpen: () => void;
  status: string | null;
}) => {
  const [isShowAll, setIsShowAll] = useState<boolean>(false);
  const [showActivityCategory, setShowActivityCategory] = useState<{
    show: boolean;
    content: string[];
  }>({
    show: false,
    content: [],
  });
  const [showQuitDialog, setShowQuitDialog] = useState<boolean>(false);
  const [showQaPanel, setShowQaPanel] = useState<boolean>(false);
  const [showProtocolPanel, setShowProtocolPanel] = useState<boolean>(false);
  const [disabledApplyBtn, setDisabledApplyBtn] = useState<boolean>(status === 'MERCHANT_OPENING');
  const handleOnShowAll = () => {
    setIsShowAll(!isShowAll);
  };

  useEffect(() => {
    setDisabledApplyBtn(status === 'MERCHANT_OPENING');
  }, [status]);

  return (
    <div className={styles['promotion-activities']}>
      <div className={styles['activities-menu']}>
        <div className={styles['menu-header']} >
          <div className={styles['menu-header-logo-opened']} />
          <div className={styles['menu-header-point']}>
            {
              POINT_LIST.map((item) => {
                return (
                  <>
                    <div className={styles['menu-header-point-opened-icon']} />
                    <div className={styles['menu-header-point-opened-desc']}>{item}</div>
                  </>
                );
              })
            }
          </div>
        </div>
        <div className={styles['menu-setting']}>
          <div className={styles['menu-setting-quit']} onClick={() => { setShowQuitDialog(true); }}>退出服务</div>
          <Dropdown
            trigger={<Button text style={{ color: '#666', fontSize: 12 }}>更多</Button>}
            triggerType={['hover', 'click']}
          >
            <Menu>
              <Menu.Item onClick={() => {
                log.addLog('promotion-setting-qa-click', 'success');
                setShowQaPanel(true);
              }}
              >常见问题
              </Menu.Item>
              <Menu.Item onClick={
                () => {
                  log.addLog('promotion-setting-protocol-click', 'success');
                  setShowProtocolPanel(true);
                }}
              >相关协议
              </Menu.Item>
            </Menu>
          </Dropdown>
        </div>
      </div>
      <div className={styles['activities-desc']}>您因参加以下活动而加入退货宝，活动期间生效</div>
      <div className={styles['activities-panel']}>
        <div className={styles['activities-panel-content']}>
          <div className={styles['activities-panel-content-main']}>
            {activitiesData && activitiesData?.length ? (
              (isShowAll ? activitiesData : activitiesData.slice(0, Math.min(3, activitiesData.length)))?.map((item) => (
                <ActivitiesItem
                  key={item.activityId}
                  status={item.status}
                  beginTime={item.beginTime}
                  endTime={item.endTime}
                  activityName={item.activityName}
                  notSupportCategoryNames={item.notSupportCategoryNames}
                  showActivityCategory={({ show, content }) => {
                    setShowActivityCategory({ show, content });
                  }}
                />
              ))
            ) : (
              <p>暂无活动</p>
            )}
          </div>
          {
            activitiesData?.length > 3 &&
            (
              <div className={styles['activities-panel-content-button']} onClick={() => { handleOnShowAll(); }}>
                {
                  isShowAll ?
                    <>
                      <div className={styles['btn-view-all']}>收起全部</div>
                      <div className={styles['btn-view-down']} />
                    </> :
                    <>
                      <div className={styles['btn-view-all']}>查看全部</div>
                      <div className={styles['btn-view-up']} />
                    </>
                }
              </div>
            )
          }
        </div>
        <div className={styles['activities-panel-button']} />
        {
          !includes(['MERCHANT_FROZEN', 'MERCHANT_QUITTING', 'MERCHANT_OPEN_SUCCEED'], status) && (
            <div className={styles['activities-panel-open']}>
              <Button
                disabled={disabledApplyBtn}
                onClick={() => {
                  setDisabledApplyBtn(true);
                  onOpen();
                }}
                type="primary"
              >
                {
                  disabledApplyBtn ? '升级长期有效中' : '升级为长期有效'
                }
              </Button>
            </div>
          )
        }
      </div>
      <Drawer
        title="常见问题"
        placement="right"
        visible={showQaPanel}
        width={600}
        onClose={() => { setShowQaPanel(false); }}
      >
        <div style={{ padding: '0 22px' }}>
          <PromotionQa isLogoOverflow noTitle />
        </div>
      </Drawer>
      <Dialog
        v2
        title="活动类目"
        visible={showActivityCategory.show}
        footer={false}
        onClose={() => { setShowActivityCategory({ show: false, content: [] }); }}
      >
        <div className={styles['activities-category']}>
          <div className={styles.content}>
            <div className={styles['content-title']}>不支持类目</div>
            <div className={styles['content-desc']}>
              {showActivityCategory?.content?.join('；') || '-'}
            </div>
          </div>
        </div>
      </Dialog>
      <Dialog
        v2
        visible={showQuitDialog}
        onClose={() => {
          log.addLog('promotion-cancel-quit-click', 'success');
          setShowQuitDialog(false);
        }}
        onOk={() => {
          log.addLog('promotion-apply-quit-click', 'success');
          window.location.replace('https://myseller.taobao.com/home.htm/starb/tmc-next/sale/seller/signed.htm');
        }}
        title={
          <div className={styles['quit-title']}>
            <div className={styles['quit-title-icon']} />退出服务
          </div>
        }
      >
        <div className={styles['quit-content']}>
          请点击【确定】至活动页面，选择【暂不加入】。部分活动可能无法退出消费者体验提升计划，以活动要求为准 。
        </div>
      </Dialog>
      <Drawer
        title="协议签署"
        placement="right"
        visible={showProtocolPanel}
        width={600}
        onClose={() => { setShowProtocolPanel(false); }}
      >
        <div style={{ padding: '0 22px' }}>
          <iframe
            width={550}
            height={710}
            style={{ border: 'none' }}
            src={PROTOCOL_URL}
          />
        </div>
      </Drawer>
    </div>
  );
};

export default PromotionActivities;
