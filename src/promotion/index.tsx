/**
* @file 退货宝大促版
* @date 2025-02-12
* <AUTHOR>
*/

import React, { useEffect, useRef, useState } from 'react';
import { Dialog, Message } from '@alifd/next';
import { queryDelivery, queryMerchantPromotionInfo } from '@/api/promotion';
import { baseOpen, baseTrail } from '@/api/open';
import { queryAlipayInfo, queryMerchantStatus } from '@/api/query';
import { get } from 'lodash-es';
import { log } from '@ali/iec-dtao-utils';
import { ByfOpenBtnMarketingTextPC, OPEN_FAILED_CODE, TRADE_ACCOUNT } from '@/constant';
import ServiceFee from './components/ServiceFee';
import PromotionActivities from './components/PromotionActivities';
import styles from './index.module.scss';
import { BaseOpenProps } from '@/types';
import { getCookie } from '@/utils/cookie';
import NoticePanel from '@/components/NoticePanel';
import { ENoticeType } from '@ali/mkt-design';

let authTimeId;
const NOTICE_TIP = [{
  type: ENoticeType.notice,
  content: '服务费价格调整为每日更新，请及时关注服务费价格变化',
}];
interface PromotionFeeProps {
  originalFeeAmount?: string;
  discountFeeAmount?: string;
  promotionFlag?: boolean;
  promotionHalfOff?: boolean;
  promotionTitle?: string;
  marketTitle?: string;
  alipayLoginId?: string | null;
}

interface ActivitiesProps {
  status: string;
  beginTime: number;
  endTime: number;
  activityName: string;
  activityId: string;
  notSupportCategoryNames: string[];
}

const Promotion = (props) => {
  const [feePanelData, setFeePanelData] = useState<PromotionFeeProps>({
    originalFeeAmount: '',
    discountFeeAmount: '',
    promotionFlag: false,
    marketTitle: '',
    alipayLoginId: null,
  });
  const [activitiesData, setActivitiesData] = useState<ActivitiesProps[]>([]);
  const [status, setStatus] = useState<string | null>(null);
  const isClickApplyBtn = useRef(false);

  // 获取营销信息
  const handleOnGetMarketInfo = async () => {
    try {
      const marketRes = await queryDelivery({ positionCode: ByfOpenBtnMarketingTextPC });
      const title = get(marketRes, 'content.items.0.fixedContent') ?? null;
      return title;
    } catch (error) {
      log.addLog('query-delivery-info', 'error', { catch: error?.message });
      return null;
    }
  };

  // 获取支付宝信息
  const handleOnGetAlipay = async () => {
    try {
      const alipayInfoData = await queryAlipayInfo({ channel: TRADE_ACCOUNT });
      const { responseCode: alipayInfoResponseCode, alipayLoginId: alipayLoginIdData } = alipayInfoData;
      if (alipayInfoResponseCode !== 'SUCCESS') {
        log.addLog('query-alipay-info', 'error', { responseCode: alipayInfoResponseCode });
        return;
      }
      return alipayLoginIdData;
    } catch (error) {
      log.addLog('query-alipay-info', 'error', { catch: error?.message });
      return null;
    }
  };

  // 获取服务费信息
  const handleOnGetBaseTrail = async () => {
    const baseTrailData = await baseTrail();
    const {
      responseCode: admitAndTrailResponseCode,
      originalFeeAmount: baseFeeAmountData,
      discountFeeAmount: discountFeeAmountData,
      promotionFlag,
      promotionTitle,
      promotionHalfOff,
    } = baseTrailData;
    if (admitAndTrailResponseCode !== 'SUCCESS') {
      Message.error('请求失败，请稍后再试！');
    }
    const marketTitle = await handleOnGetMarketInfo();
    const alipayInfo = await handleOnGetAlipay();
    setFeePanelData({
      originalFeeAmount: baseFeeAmountData,
      discountFeeAmount: discountFeeAmountData,
      promotionFlag,
      promotionTitle,
      promotionHalfOff,
      marketTitle,
      alipayLoginId: alipayInfo,
    });
  };

  // 获取活动信息
  const handleOnGetActivities = async () => {
    try {
      const promotionsData = await queryMerchantPromotionInfo();
      const { responseCode: promotionsResponseCode, promotionSignInfoDTOList } = promotionsData;
      if (promotionsResponseCode !== 'SUCCESS') {
        log.addLog('query-promotions-info', 'error', { responseCode: promotionsResponseCode });
        Message.error('请求失败，请稍后再试！');
        return;
      }
      setActivitiesData(promotionSignInfoDTOList);
    } catch (error) {

    }
  };

  // 获取用户日常状态
  const handleOnGetStatus = async (checkStatus) => {
    try {
      const statusInfoData = await queryMerchantStatus();
      const { status: statusData, responseCode: statusResponseCode, failedCode: statusFailedCode } = statusInfoData;
      if (statusResponseCode !== 'SUCCESS') {
        return;
      }
      setStatus(statusData);
      switch (statusData) {
        case 'MERCHANT_OPENING':
          checkStatus();
          break;
        case 'MERCHANT_OPEN_FAILED':
          isClickApplyBtn.current && Dialog.show({
            v2: true,
            footer: false,
            onClose: () => {
              window.location.reload();
            },
            content: (
              <div className={styles['dialog-failed']}>
                <div className={styles['dialog-failed-icon']} />
                <div className={styles['dialog-failed-title']} >开通失败</div>
                <div className={styles['dialog-failed-desc']} >
                  {statusFailedCode ? OPEN_FAILED_CODE[statusFailedCode] || OPEN_FAILED_CODE?.DEFAULT_FRONT_ERROR : OPEN_FAILED_CODE?.DEFAULT_FRONT_ERROR}
                </div>
              </div>
            ),
          });
          return;
        case 'MERCHANT_FROZEN':
        case 'MERCHANT_QUITTING':
        case 'MERCHANT_OPEN_SUCCEED':
          props.history.push('/manage');
          return;
        default:
          break;
      }
    } catch (error) {
      log.addLog('base-admit-and-trail', 'error', { catch: error?.message });
    }
  };

  const checkUserStatus = () => {
    const checkStatus = () => {
      authTimeId = setTimeout(() => handleOnGetStatus(checkStatus), 2000);
    };
    handleOnGetStatus(checkStatus);
  };

  // 日常开通申请
  const handleOnOpen = async () => {
    try {
      const params: BaseOpenProps = {
        identifier: `${getCookie()}-${Math.floor(Date.now())}`,
        showFeeAmount: feePanelData.originalFeeAmount,
        promotionHalfOff: feePanelData.promotionHalfOff,
      };
      feePanelData?.promotionFlag && (params.showDiscountFeeAmount = feePanelData.discountFeeAmount);
      params.openChannel = 'qianniu_pc_promotion';
      const applyData = await baseOpen(params);
      const { responseCode } = applyData;
      if (responseCode !== 'SUCCESS') {
        log.addLog('promotion-apply-fail', 'error', { responseCode });
        Dialog.show({
          v2: true,
          footer: false,
          onClose: () => {
            window.location.reload();
          },
          content: (
            <div className={styles['dialog-failed']}>
              <div className={styles['dialog-failed-icon']} />
              <div className={styles['dialog-failed-title']} >开通失败</div>
              <div className={styles['dialog-failed-desc']} >
                {responseCode ? OPEN_FAILED_CODE[responseCode] || OPEN_FAILED_CODE?.DEFAULT_FRONT_ERROR : OPEN_FAILED_CODE?.DEFAULT_FRONT_ERROR}
              </div>
            </div>
          ),
        });
        return;
      }
      checkUserStatus();
    } catch (error) {
      log.addLog('promotion-apply-fail', 'error', { message: error?.message });
      Dialog.show({
        v2: true,
        footer: false,
        onClose: () => {
          window.location.reload();
        },
        content: (
          <div className={styles['dialog-failed']}>
            <div className={styles['dialog-failed-icon']} />
            <div className={styles['dialog-failed-title']} >开通失败</div>
            <div className={styles['dialog-failed-desc']} >
              {OPEN_FAILED_CODE?.DEFAULT_FRONT_ERROR}
            </div>
          </div>
        ),
      });
    }
  };

  useEffect(() => {
    handleOnGetBaseTrail();
    handleOnGetActivities();
  }, []);

  useEffect(() => {
    checkUserStatus();
    return () => clearTimeout(authTimeId);
  }, []);

  return (
    <div className={styles.promotion}>
      <NoticePanel data={NOTICE_TIP} />
      <PromotionActivities
        onOpen={() => {
          isClickApplyBtn.current = true;
          handleOnOpen();
        }}
        activitiesData={activitiesData}
        status={status}
      />
      <ServiceFee
        promotionFlag={feePanelData.promotionFlag}
        discountFeeAmount={feePanelData.discountFeeAmount}
        originalFeeAmount={feePanelData.originalFeeAmount}
        marketTitle={feePanelData.marketTitle}
        alipayLoginId={feePanelData.alipayLoginId}
      />
    </div>
  );
};

export default Promotion;
