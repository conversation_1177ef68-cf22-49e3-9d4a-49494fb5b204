// 开通状态枚举
export type OpenStatus = 'null' | 'MERCHANT_OPENING' | 'MERCHANT_OPEN_SUCCEED' | 'MERCHANT_OPEN_FAILED' | 'MERCHANT_FROZEN' | 'MERCHANT_QUITTING' | 'MERCHANT_QUIT_SUCCEED';
export type AlipayChannel = 'TRADE_ACCOUNT' | 'FIN_ACCOUNT';
export type FixedPrice = 'YEAR' | 'HALF_YEAR' | 'SEASON';

interface BaseResponse {
  success?: boolean;
  responseCode?: string;
  responseMessage?: string;
}

export interface MerchantServiceStatusResponse extends BaseResponse {
  status: OpenStatus;
  fixedPriceType: string;
  openFixedPrice?: boolean;
  failedCode?: string;
  customerFreezeCode?: string | null;
  giveType: string | null;
  openChannel?: string | null;
  currFixedPriceExpireSoon?: boolean | null;
  currFixedPriceRenewal?: boolean | null;
  currFixedPriceExpireTime?: number | string | null;
}

export interface PromotionDetailList {
  attributes?: Map<string, Object>;
  promotionAmount?: number;
  promotionId?: string;
  promotionTitle?: string;
}

export interface BaseAdmitResponse extends BaseResponse {
  isAdmit: boolean;
  hasHighRiskRefund: boolean;
  openFixedPrice?: null | boolean;
  originalFeeAmount: string;
  fixedPriceType: FixedPrice;
  discountFeeAmount?: string;
  promotionFlag?: boolean;
  customerRejectCode?: string;
  promotionDetailList?: PromotionDetailList[];
  exemptionAmount?: string;
}

export interface AlipayInfoRequest {
  channel: AlipayChannel;
}

export interface ServiceOrderInfoRequest {
  mainOrderId: string | null;
}

export interface ClaimOrderListRequest {
  mainOrderId: string | null;
  sceneList: string[];
}

export interface ServiceOrderItem {
  itemId?: string;
  itemName?: string;
  itemNum?: string;
  itemUrl?: string;
  itemPicUrl?: string;
  skuId?: string;
  skuName?: string;
}

export interface ServiceInfo {
  serviceNo?: string | null;
  effectiveTime?: number | null;
  expireTime?: number | null;
  actualFeeAmount?: string | null;
  originalFeeAmount?: string | null;
  selfPostageGuaranteedAmount?: string | null;
  promotion?: boolean;
  promotionTitles?: string[] | null;
  promotionDetails: any[];
}

export interface OrderInfoProps {
  mainOrderId: string | null;
  subOrderId: string | null;
  orderPayTime: number | null;
}

export interface ServiceOrderInfoResponse extends BaseResponse {
  serviceStatus: string;
  orderInfo: OrderInfoProps;
  serviceInfo: ServiceInfo;
  serviceOrderItemList: ServiceOrderItem[];
}

export interface ClaimDetailInfoListItem {
  compensationStatus?: string;
  specialDescCode?: string | boolean;
  compensatedAmount?: string | null;
  payee?: string | null;
  applyTime: number | null;
  compensatedTime: number | null;
  compensationSource?: string;
}

export interface ClaimOrderListResponse extends BaseResponse {
  claimDetailInfoList: ClaimDetailInfoListItem[];
}

export interface AlipayInfoResponse extends BaseResponse {
  alipayLoginId: string;
}

export interface BaseOpenProps {
  identifier: string;
  fixedPriceType?: string;
  showFeeAmount?: string;
  openChannel?: string;
  showDiscountFeeAmount?: string;
  isChecked?: boolean;
  promotionHalfOff?: boolean;
}

export interface BaseOpenResponse extends BaseResponse {
  applicationNo?: string;
  originalFeeAmount?: string;
  discountFeeAmount?: string;
  promotionFlag?: boolean;
}

export interface BaseTrailResponse extends BaseResponse {
  originalFeeAmount?: string;
  discountFeeAmount?: string;
  promotionFlag?: boolean;
  promotionHalfOff?: boolean;
  promotionTitle?: string;
  promotionDetailList?: PromotionDetailList[];
  exemptionAmount?: string;
}

export interface FixedAdmitResponse extends BaseResponse {
  originalFeeAmount?: string;
  fixedPriceType?: string | null;
  effectTime?: string | null;
  expireTime?: string | null;
}

export interface FixedRenewalAdmitResponse extends BaseResponse {
  renewalAdmitInfoList: any[];
}

export interface FixedAdmitAndTrailResponse extends BaseResponse {
  originalFeeAmount?: string;
  fixedPriceType?: FixedPrice;
  effectTime: number | null;
  expireTime: number | null;
  discountFeeAmount?: string;
  promotionTitle?: string;
  promotionFlag?: boolean;
  isAdmit?: boolean;
}

export interface FixedOpenRequest {
  identifier: string;
  effectTime?: number | undefined | string;
  expireTime?: number | undefined | string;
  fixedPriceType?: string | undefined;
  showFeeAmount?: string | undefined;
  discountFeeAmount?: string | undefined;
}

export interface BaseAndFixedOpenRequest {
  identifier: string;
  fixedPriceEffectTime?: number | string;
  fixedPriceExpireTime?: number | string;
  fixedPriceType?: string ;
  showFeeAmount?: string ;
  showDiscountFeeAmount?: string;
  umidToken?: string;
  openChannel?: string;
}

export type FixedOpenResponse = BaseResponse;

export interface FixedTrailRequest {
  fixedPriceType?: string;
  openChannel?: string;
}

export interface FixedTrailResponse extends BaseResponse {
  fixedPriceType: string;
  originalFeeAmount: string;
  effectTime?: number | null;
  expireTime?: number | null;
  discountFeeAmount: string;
  promotionFlag: boolean;
}

export interface QueryMerchantServiceResponse extends BaseResponse {
  totalCompensationAmount?: string;
  totalServiceAmount?: string;
  totalGuaranteeDays?: number | null;
  totalBuyersOfNotGive?: string | null;
}

export interface QueryMerchantArrearsResponse extends BaseResponse {
  arrearsCalDate?: number | null;
  arrearsAmountMoreThan6Hours?: string | null;
  showArrears: boolean;
  arrears: boolean;
}


export interface BaseQuitRequest {
  identifier: string;
  scene: string;
  reason: string;
}

export interface BaseQuitResponse extends BaseResponse {
  applicationNo?: string;
}


export interface QueryDeliveryRequest {
  positionCode: string;
}

interface deliveryItems {
  fixedContent?: string;
}

export interface QueryDeliveryResponse {
  content: {
    items: deliveryItems[];
  };
}

export interface GiveAdmitRequest {
  openChannel: string;
}

export interface GiveAdmitResponse extends BaseResponse {
  status: string;
}

export interface PartSettingRequest {
  channel: string;
  identifier: string;
  giveType: string;
}

export type PartSettingResponse = BaseResponse;

export interface CouponListRequest {
  useStatus: string;
}

export interface CouponListResponse extends BaseResponse {
  merchantCouponDetailList: any;
  currentPage: number;
  totalRecord: number;
}

export interface CouponByIdRequest {
  couponId: string;
}

export interface DataIndicatorListResponse extends BaseResponse {
  indicatorProcessData: any[];
  endTime: number;
}

export interface CouponByIdResponse extends BaseResponse {
  merchantCouponDetail: any;
}

export interface PromotionSignInfoDTO {
  status: string;
  beginTime: number;
  endTime: number;
  activityName: string;
  activityId: string;
  notSupportCategoryNames: string[];
}

export interface QueryMerchantPromotionInfoResponse extends BaseResponse {
  open?: boolean;
  promotionSignInfoDTOList: PromotionSignInfoDTO[];
}

export interface PriceInfo {
  priceType?: string;
  title?: string;
  subTitle?: string;
  desc?: string;
  isAdmit?: Boolean;
  promotionFlag?: Boolean;
  originalFeeAmount?: string;
  exemptionAmount?: string;
  discountFeeAmount?: string;
  effectTime?: string | null;
  expireTime?: string | null;
}
