/**
* @file 服务设置组件
* @date 2024-12-13
* <AUTHOR>
*/

import React, { useEffect, useState } from 'react';
import { Dialog, Radio } from '@alifd/next';
import styles from './index.module.scss';
import { log } from '@ali/iec-dtao-utils';

const RadioGroup = Radio.Group;

const PartSetting = ({
  isShow,
  giveType,
  setIsShow,
  onChangeSetting,
}: {
  isShow: boolean;
  giveType?: string | null;
  setIsShow: (show: boolean) => void;
  onChangeSetting: (option) => void;
}) => {
  const [option, setOption] = useState<string | null>(giveType ?? 'ALL');
  const theme = window?.QN_WORKBENCH_CFG?.domain?.name === 'tmall' ? 'tmall' : 'taobao';

  // 各弹框打点
  useEffect(() => {
    // 全店设置弹框曝光
    if (isShow && (giveType === 'ALL' || giveType === null)) {
      log.addLog('part-setting-all', 'success');
    }
    // 部分设置弹框曝光
    if (isShow && (giveType === 'PART')) {
      log.addLog('part-setting-part', 'success');
    }
  }, [isShow, giveType]);

  useEffect(() => {
    setOption(giveType ?? 'ALL');
  }, [giveType]);

  useEffect(() => {
    !isShow && setOption(giveType ?? 'ALL');
  }, [isShow]);

  const handleOnChangeSetting = (value) => {
    setOption(value);
  };

  const okButtonStyle = {
    tmall: 'lightOkBtn',
    taobao: 'okBtn',
  };
  const cancelButtonStyle = {
    tmall: 'lightCancelBtn',
    taobao: 'cancelBtn',
  };

  return (
    <div>
      <Dialog
        v2
        title={'服务设置'}
        visible={isShow}
        width={400}
        height={268}
        okProps={{ children: '确认', className: option === 'ALL' ? styles[okButtonStyle[theme]] : styles[cancelButtonStyle[theme]] }}
        cancelProps={{ children: '取消', className: option === 'ALL' ? styles[cancelButtonStyle[theme]] : styles[okButtonStyle[theme]] }}
        className={styles['part-setting-dialog']}
        style={{ width: '400px' }}
        onOk={() => {
          log.addLog('dialog-part-setting', 'click', { from: option });
          onChangeSetting(option);
          setIsShow(false);
        }}
        footerActions={option === 'ALL' ? ['ok'] : ['ok', 'cancel']}
        onCancel={() => {
          log.addLog('dialog-part-setting-cancel', 'click', { from: giveType });
          setIsShow(false);
        }}
        onClose={() => {
          log.addLog('dialog-part-setting-close', 'click', { from: giveType });
          setIsShow(false);
        }}
      >
        <RadioGroup defaultValue={giveType ?? 'ALL'} onChange={(value) => handleOnChangeSetting(value)} aria-labelledby="groupId">
          <Radio id="ALL" value="ALL">
            全店开通退货宝<span className={styles.recommendedTag}>推荐</span>
          </Radio>
          <Radio id="PART" value="PART">
            部分开通退货宝：针对精选用户提供服务
            <div className={styles.partOptionsDesc}>(为保障消费者体验，本设置仅在日常生效，特殊时段如大促期间仍保持全店开通，感谢您的理解)</div>
          </Radio>
        </RadioGroup>
      </Dialog>
    </div>
  );
};

export default PartSetting;
