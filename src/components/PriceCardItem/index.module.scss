.item-content {
  display: flex;
  position: relative;
  flex-direction: column;
  border-radius: 12px;
  border: 1px solid #e4e6ed;
}

.item-tag {
  width: 120px;
  height: 18px;
  position: absolute;
  right: -1px;
  top: -8px;
  border-radius: 9px 9px 0 9px;
  background: #c45902;
  text-align: center;
  line-height: 16px;
  span {
    color: #fff;
    font-size: 12px;
    font-weight: normal;
    line-height: 12px;
    text-align: center;
  }
}

.item-content.DAILY-selected {
  background: linear-gradient(0deg, rgba(61, 94, 255, 0.1) 0%, rgba(61, 94, 255, 0) 100%);
  border: 1px solid #3d5eff;
}

.item-content.YEAR-selected {
  background: linear-gradient(180deg, #fff7ed 0%, #fff0f0 100%);
  border: 1px solid #c45902;
}

.item-content.MONTH-selected {
  background: linear-gradient(180deg, #fff7ed 0%, #fff0f0 100%);
  border: 1px solid #c45902;
}

.item-title {
  display: flex;
  flex-direction: row;
  justify-content: center;
  margin-top: 12px;
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  color: #111;
}

.item-price {
  margin-top: 16px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: flex-end;

  .baseTrailAmount {
    display: flex;
    align-items: flex-end;
    gap: 3px;

    .amount {
      font-size: 24px;
      line-height: 30px;
      font-weight: 500;
      color: #111;
      margin-right: 3px;
    }

    .unit {
      font-size: 12px;
      color: #111;
    }
  }
  .base-trail-origin {
    display: flex;
    flex-direction: row;
    text-decoration: line-through;
    color: #666;
    font-size: 12px;
  }
}

.amount-content-item {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  gap: 3px;
}

.highlight-amount {
  line-height: 24px;
  font-size: 24px;
  font-weight: 500;
  letter-spacing: normal;
  color: #111;
}

.highlight-unit {
  font-size: 12px;
  font-weight: normal;
}

.highlight-amount.DAILY-selected,
.highlight-unit.DAILY-selected {
  color: #3d5eff;
}

.highlight-amount.MONTH-selected,
.highlight-amount.MONTH,
.highlight-unit.MONTH-selected,
.highlight-unit.MONTH {
  color: #c45902;
}

.highlight-amount.YEAR-selected,
.highlight-amount.YEAR,
.highlight-unit.YEAR-selected,
.highlight-unit.YEAR {
  color: #c45902;
}

.base-amount {
  font-size: 12px;
  text-decoration: line-through;
  color: #999;
  font-weight: 400;
}

.item-rule {
  display: flex;
  flex-direction: row;
  justify-content: center;
  margin: 14px;

  .item-rule-desc {
    font-size: 12px;
    color: #131313;
  }

  .item-rule-period {
    display: flex;
    flex-direction: row;
    font-size: 12px;
    color: #131313;
  }
}

.item-footer {
  display: flex;
  border-radius: 0 0 6px 6px;
  justify-content: center;
  align-items: center;
  height: 38px;
  flex-direction: row;
  font-size: 14px;
  line-height: 20px;
  background-color: #f8f9fa;
}

.item-footer.DAILY-selected {
  font-weight: 500;
  color: #3d5eff;
  border-radius: 0 0 6px 6px;
  background: rgba(61, 94, 255, 0.12);
}
.item-footer.YEAR-selected {
  font-weight: 500;
  color: #c45902;
  border-radius: 0 0 6px 6px;
  background: rgba(255, 210, 189, 0.35);
}
.item-footer.MONTH-selected {
  font-weight: 500;
  color: #c45902;
  border-radius: 0 0 6px 6px;
  background: rgba(255, 210, 189, 0.35);
}
