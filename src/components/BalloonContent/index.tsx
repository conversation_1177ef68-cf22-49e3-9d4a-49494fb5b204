import React from 'react';
import { Balloon } from '@alifd/next';
import styles from './index.module.scss';

interface BalloonContentProps {
  icon: React.ReactNode | any;
  align: 't' | 'r' | 'b' | 'l' | 'tl' | 'tr' | 'bl' | 'br' | 'lt' | 'lb' | 'rt' | 'rb' | undefined;
  contentChildren: React.ReactNode;
  contentWidth?: number;
}

const BalloonContent: React.FC<BalloonContentProps> = ({ icon, align, contentChildren, contentWidth = 200 }) => {
  return (
    <div className={styles['balloon-content-panel']}>
      <Balloon
        v2
        trigger={icon}
        align={align}
        triggerType={['click', 'hover']}
        closable={false}
      >
        <div className={styles['balloon-content-panel-desc']} style={{ width: `${contentWidth}px` }}>
          {contentChildren}
        </div>
      </Balloon>
    </div>
  );
};


export default BalloonContent;
