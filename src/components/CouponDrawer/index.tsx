/**
* @file index
* @date 2025-03-16
* <AUTHOR>
*/

import React, { useEffect, useState } from 'react';
import styles from './index.module.scss';
import { Loading } from '@alifd/next';
import { queryCouponById } from '@/api/query';
import { log } from '@ali/iec-dtao-utils';
import dayjs from 'dayjs';
import { money_US } from '@ali/iec-dtao-utils/esm/common/number';
import { renderPrice } from '@/utils/tools';

interface CouponDetail {
  couponTitle?: string;
  couponSubTitle?: string;
  conditionDescription?: string;
  rule?: string[];
  totalPromotionQuota: any;
  maxUseTimes: string | number;
}

const renderLimitText = (item) => {
  const text: string[] = [];
  if (item?.totalPromotionQuota?.value) {
    text.push(`【限制使用额度${money_US(item?.totalPromotionQuota?.value) || '-'}元】`);
  }
  if (item?.maxUseTimes && Number(item.maxUseTimes) > 0) {
    text.push(`【限制核销${item.maxUseTimes}次以内】`);
  }
  if (text?.length) {
    return text.join('');
  }
  return null;
};


const CouponDrawer = ({ currentId }: { currentId: string | null }) => {
  const [detailData, setDetailData] = useState<CouponDetail | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const handleOnQueryCouponId = async (id) => {
    try {
      setIsLoading(true);
      const couponIdData = await queryCouponById({
        couponId: id,
      });
      setIsLoading(false);
      const { responseCode, merchantCouponDetail } = couponIdData;
      if (responseCode !== 'SUCCESS') {
        log.addLog('query-coupon-list', 'error', { responseCode });
        setDetailData(null);
        return;
      }
      const price = renderPrice(merchantCouponDetail);
      const dateRange = (merchantCouponDetail?.effectTime && merchantCouponDetail?.expireTime) ?
        `优惠有效期：${dayjs(merchantCouponDetail.effectTime).format('YYYY.MM.DD HH:mm')}～${dayjs(merchantCouponDetail.expireTime).format('YYYY.MM.DD HH:mm')}`
        : '优惠有效期：--';
      const descriptionList = merchantCouponDetail?.description?.split('\n');

      setDetailData({
        couponTitle: merchantCouponDetail?.title,
        couponSubTitle: `${price?.priceText}${price?.priceUnit ?? ''}券`,
        conditionDescription: merchantCouponDetail?.conditionDescription,
        rule: [dateRange, ...descriptionList],
        totalPromotionQuota: merchantCouponDetail?.totalPromotionQuota,
        maxUseTimes: merchantCouponDetail?.maxUseTimes,
      });
    } catch (error) {
      log.addLog('query-coupon-list', 'error', { catch: error?.message });
      setIsLoading(false);
      setDetailData(null);
    }
  };

  useEffect(() => {
    if (currentId) {
      handleOnQueryCouponId(currentId);
    }
  }, [currentId]);

  return (
    <Loading tip="large" size="large" visible={isLoading}>
      <div className={styles.couponDrawer}>
        <div className={styles.couponDrawerPanel}>
          <div className={styles.couponDrawerTitle}>
            {detailData?.couponTitle || '-'}
          </div>
          <div className={styles.couponDrawerContent}>
            {detailData?.couponSubTitle ? `${detailData?.couponSubTitle}，` : ''}{detailData?.conditionDescription || '-'}
            {renderLimitText(detailData) ?? ''}
          </div>
        </div>
        <div className={styles.couponDrawerPanel}>
          <div className={styles.couponDrawerTitle}>使用规则</div>
          <div className={styles.couponDrawerContent}>
            {
              detailData?.rule && detailData?.rule.map((item, index) => {
                return (
                  <div key={index}>
                    {index + 1}. {item}
                  </div>
                );
              })
            }
          </div>
        </div>
        <div className={styles.couponDrawerPanel}>
          <div className={styles.couponDrawerTitle}>
            其他注意事项
          </div>
          <div className={styles.couponDrawerContent}>
            若在获取或者使用过程中，如存在违规行为（如作弊领取、恶意套现、刷取信誉、虚假交易等），上海淘天商业管理有限公司有权取消您的优惠券使用资格，若已使用上海淘天商业管理有限公司有权追回；如给上海淘天商业管理有限公司造成损失或不良影响的，上海淘天商业管理有限公司保留追究赔偿的权利。活动期间如出现不可抗力或情势变更的情况，上海淘天商业管理有限公司无需为此承担赔偿责任或进行补偿。
          </div>
        </div>
      </div>
    </Loading>
  );
};

export default CouponDrawer;
