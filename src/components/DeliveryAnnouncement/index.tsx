/**
 * @file 用增公告栏组件
 */

import React, { useMemo } from 'react';
import { Announcement } from '@alife/iec-dtao-delivery-pc';

import { PRODUCT_CODE, HOME_NOTICE_POSITION_CODE } from '@/constant';

import styles from './index.module.scss';

export default function DeliveryAnnouncement(props) {
  const { isOpenFixed, isFixedAdmit } = props;

  const renderAnnouncement = useMemo(() => {
    try {
      let announcementClassName = '';
      if (isOpenFixed) {
        announcementClassName = styles.fixedDeliveryAnnouncement;
      } else {
        announcementClassName = isFixedAdmit ? styles.deliveryAnnouncement : styles.fixedDeliveryAnnouncement;
      }
      return (
        <Announcement
          className={announcementClassName}
          productType={PRODUCT_CODE}
          positionCode={HOME_NOTICE_POSITION_CODE}
        />
      );
    } catch (e) {
      return null;
    }
  }, [isOpenFixed, isFixedAdmit]);

  return renderAnnouncement;
}
