import React from 'react';
import styles from './index.module.scss';

const ITEM_IMG = {
  divider: 'https://img.alicdn.com/imgextra/i2/6000000000950/O1CN015PKp9o1It9IpQtCmm_!!6000000000950-2-d2c.png',
  arrow: 'https://img.alicdn.com/imgextra/i1/O1CN01VXQFOF1FiCznmCaFh_!!6000000000520-2-tps-20-20.png',
  subsidyLabel: 'https://img.alicdn.com/imgextra/i3/6000000002927/O1CN01ep27PT1XUcKEUMDqD_!!6000000002927-2-d2c.png',
  iconSrc: 'https://img.alicdn.com/imgextra/i3/O1CN015Rg9DP1JOIEiJ8e7a_!!6000000001018-2-tps-120-120.png',
};

const COUPON_STATUS = {
  COUPON_AVAILABLE: '待生效',
  COUPON_USING: '生效中',
  COUPON_USED: '已使用',
  COUPON_EXPIRE: '已过期',
  COUPON_INVALID: '已失效',
};

const COUPON_IMG = {
  COUPON_AVAILABLE: 'https://img.alicdn.com/imgextra/i2/O1CN01emLWA91f7jSirDR3s_!!6000000003960-2-tps-180-180.png',
  COUPON_USED: 'https://img.alicdn.com/imgextra/i1/O1CN01cII0in1neiuyOmCRq_!!6000000005115-2-tps-180-180.png',
  COUPON_INVALID: 'https://img.alicdn.com/imgextra/i1/O1CN01YlD1IG26Ti5py5aBp_!!6000000007663-2-tps-180-180.png',
  COUPON_EXPIRE: 'https://img.alicdn.com/imgextra/i2/O1CN01SAydSJ1rqPUIM2Btq_!!6000000005682-2-tps-180-180.png',
};

interface CouponCardProps {
  label: string;
  price: {
    priceText: string;
    priceUnit: string | null;
  };
  title: string;
  dateRange: string;
  limit: string;
  rule: string;
  status: string;
  id: string;
}

const CouponCardBg = {
  // 待生效
  COUPON_AVAILABLE: {
  },
  // 生效中
  COUPON_USING: {
  },
  // 已使用
  COUPON_USED: {
    opacity: 0.7,
  },
  // 已过期
  COUPON_EXPIRE: {
    opacity: 0.7,
    filter: 'grayscale(100%)',
  },
  // 已失效
  COUPON_INVALID: {
    opacity: 0.7,
    filter: 'grayscale(100%)',
  },
};

const CouponActivityItem = ({ data, onClick }: { data: CouponCardProps; onClick: (id) => void }) => {
  return (
    <div
      className={styles.couponCard}
      style={{
        opacity: CouponCardBg[data.status]?.opacity ?? 1,
        filter: CouponCardBg[data.status]?.filter ?? null,
      }}
      onClick={() => { onClick(data.id); }}
    >
      <div className={styles.couponCardCorner}>
        {
          ['COUPON_USING'].indexOf(data.status) > -1 ? (
            <>
              <img className={styles.couponIcon} src={ITEM_IMG.iconSrc} />
              <span className={styles.couponStatus}>{COUPON_STATUS[data.status]}</span>
            </>
          ) : (
            <>
              <img className={styles.couponUsedIcon} src={COUPON_IMG[data.status]} />
            </>
          )
        }
      </div>
      <div className={styles.couponCardTag}>
        <span>{data.label}</span>
      </div>
      <div className={styles.couponCardContent}>
        <div className={styles.couponCardContentAmount}>
          {data.price?.priceText}
          {
            data.price?.priceUnit && <span>{data.price?.priceUnit}</span>
          }
        </div>
        <div
          className={styles.couponCardContentDetail}
          style={{
            marginLeft: data?.price?.priceUnit ? 22 : 31,
          }}
        >
          <div className={styles.couponCardContentDetailTitle}>
            {data.title}
          </div>
          <div className={styles.couponCardContentDetailRange}>
            {data.dateRange}
          </div>
          <div className={styles.couponCardContentDetailExtra}>
            {data.limit}
          </div>
        </div>
      </div>
      <div className={styles.couponCardContentRule}>
        {data.rule}
        <img className={styles.arrowIcon} src={ITEM_IMG.arrow} />
      </div>
    </div>
  );
};

export default CouponActivityItem;
