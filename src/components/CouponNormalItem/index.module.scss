.couponCard {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  width: 373px;
  height: 134px;
  background: linear-gradient(90deg, #fff9f8 0%, #fff8f5 100%);
  border: 1px solid #ffe8dd;
  border-radius: 10px;
  overflow: hidden;

  .couponCardCorner {
    position: relative;
    right: 0;
    height: 0;

    .couponIcon {
      width: 60px;
      height: 60px;
      position: absolute;
      right: 0;
      // align-self: flex-start;
      // z-index: 2;
    }
    .couponUsedIcon {
      width: 90px;
      height: 90px;
      position: absolute;
      right: -20px;
      top: -24px;
      opacity: 0.7;
    }
    .couponStatus {
      color: #fff;
      font-size: 12px;
      font-weight: 500;
      line-height: 12px;
      position: absolute;
      right: 6px;
      top: 15px;
      // z-index: 3;
      white-space: nowrap;
      transform: rotate(45deg);
    }
  }

  .couponCardTag {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 67px;
    height: 18px;
    border-radius: 10px 0 10px 0;
    background: linear-gradient(90deg, rgba(255, 152, 129, 0.2) 0%, rgba(255, 109, 77, 0.2) 100%);
    span {
      font-size: 12px;
      line-height: 12px;
      color: #ff7253;
    }
  }

  .couponCardContent {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    margin-left: 25px;
    height: 80px;

    .couponCardContentAmount {
      font-family: "AlibabaFontMd";
      font-size: 36px;
      font-weight: bold;
      line-height: 36px;
      letter-spacing: 0;
      color: #ff7253;

      span {
        font-size: 12px;
        font-weight: normal;
        line-height: 12px;
        color: #ff7253;
        margin-left: 4px;
      }
    }

    .couponCardContentDetail {
      display: flex;
      flex-direction: column;
      // margin-left: 10px;
      gap: 6px;

      .couponCardContentDetailTitle {
        display: flex;
        font-size: 14px;
        font-weight: 600;
        line-height: 14px;
        color: #222;
      }

      .couponCardContentDetailRange {
        display: flex;
        font-size: 12px;
        font-weight: normal;
        line-height: 12px;
        color: #666;
      }

      .couponCardContentDetailExtra {
        display: flex;
        flex-direction: row;
        font-size: 12px;
        line-height: 12px;
        color: #ff7253;
      }
    }
  }

  .couponCardSplitLine {
    display: flex;
    justify-content: center;
    margin: 0 auto;
    width: 349px;
    height: 0.8px;
    background: rgba(216, 216, 216, 0.01);
    border: 0.8px dashed #ffd9d1;
  }

  .couponCardContentRule {
    display: flex;
    flex-direction: row;
    font-size: 12px;
    line-height: 12px;
    color: #ff7253;
    margin: 12px;

    img {
      width: 10px;
      height: 10px;
      align-self: center;
    }
  }
}
