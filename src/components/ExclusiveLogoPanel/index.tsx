import React, { useCallback } from 'react';
import styles from './index.module.scss';

const imgList = [
  'https://img.alicdn.com/imgextra/i1/O1CN01OmxvnU22AEjgGRAyp_!!6000000007079-0-tps-480-640.jpg',
  'https://img.alicdn.com/imgextra/i3/O1CN01D62XDF21TBVOeg0yP_!!6000000006985-0-tps-480-640.jpg',
  'https://img.alicdn.com/imgextra/i4/O1CN01dHfJsx1nK72Sy37NS_!!6000000005070-0-tps-480-640.jpg',
  'https://img.alicdn.com/imgextra/i1/O1CN01N7BClJ1m7STmmEG6B_!!6000000004907-0-tps-480-640.jpg',
];

interface ExclusiveLogoPanelProps {
  isOverflow?: boolean;
}

const ExclusiveLogoPanel = (props: ExclusiveLogoPanelProps) => {
  const getClassName = useCallback(() => {
    if (props?.isOverflow) {
      return `${styles['exclusive-logo-panel']} ${styles['exclusive-logo-panel-overflow']}`;
    }
    return styles['exclusive-logo-panel'];
  }, [props?.isOverflow]);

  return (
    <div className={getClassName()}>
      <div className={styles.content}>
        {
          imgList.map((img) => {
            return <img src={img} />;
          })
        }
      </div>
    </div>
  );
};

export default ExclusiveLogoPanel;
