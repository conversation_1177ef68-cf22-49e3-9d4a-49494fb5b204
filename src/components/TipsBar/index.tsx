import React from 'react';
import styles from './index.module.scss';
import { Slider } from '@alifd/next';

const TipsBar = ({ content }) => {
  if (!content?.length) {
    return <></>;
  }

  return (
    <div className={styles['tips-bar']}>
      <div className={styles['tips-bar-icon']} />
      <div className={styles['tips-bar-content']} >
        <Slider autoplay autoplaySpeed={3000} infinite slideDirection="ver" dots={false} arrows={false} >
          {
            content.map((item, index) => <div key={index}>{item}</div>)
          }
        </Slider>
      </div>
    </div>
  );
};

export default TipsBar;
