import React from 'react';

import ExclusiveLogoPanel from '../ExclusiveLogoPanel';

import styles from './index.module.scss';

const qaList = {
  Q1: {
    title: <>Q：退货宝服务保障90天是什么意思？90天内消费者都可以退换货吗？</>,
    content: <>A：90天保障期是行业退货包运费服务的通用标准。退货宝并不影响商品现有的退换货规则。每笔订单的退换货时效依据三包法及新消法的相关规定、《淘宝网七天无理由退货规范》、《淘宝平台争议处理规则》等相关规则，以及买卖双方协商/卖家承诺。</>,
  },
  Q2: {
    title: <>Q：退货宝专属标识是怎么展示的？</>,
    content: <>A：退货宝专属标识的展示如下：</>,
    logoPanel: true,
  },
  Q3: {
    title: <>Q：退货宝和退货运费险会重复出单和扣费吗？</>,
    content: <>A：加入成功后，若您店铺已订购退货运费险，运费险（仅小件）自动退出，新产生的订单运费险不再扣费。</>,
  },
  Q4: {
    title: <>Q：消费者服务体验提升计划服务费价格是怎么确定的？</>,
    content: <>A：服务费用将基于近期店铺经营类目及规模、交易及售后等环节（如退货退款）的服务体验综合确定，并可能基于商家经营情况的变化进行调整。</>,
  },
  Q5: {
    title: <>Q：服务费是怎么收取的？</>,
    content: <>A：服务费按单收取，每笔实物订单在线确认发货后，系统自动从卖家支付宝账户扣除服务费；如未发货，则不收取服务费。</>,
  },
  Q6: {
    title: <>Q：加入消费者体验提升计划后，为什么有些订单不支持退货运费保障？</>,
    content:
  <>
    <div>A：加入消费者体验提升计划后，部分订单可能因为以下原因不支持运费保障：</div>
    <div>&nbsp; &nbsp; &nbsp; · 每笔订单（主订单/父订单维度，非子订单维度）仅可获得一次退货宝权益，超过部分不予保障； </div>
    <div>&nbsp; &nbsp; &nbsp; · 运费金额超过首重标准的，超过部分不予保障；</div>
    <div>&nbsp; &nbsp; &nbsp; · 在商家发货90天后申请退换货的；</div>
    <div>&nbsp; &nbsp; &nbsp; · 未发生退换货或未垫付退换货物流费用的；</div>
    <div>&nbsp; &nbsp; &nbsp; · 未上传真实退换货快递单号或退换货的物流存在异常（包括但不限于物流单号无效/已被使用过/物流轨迹或时间异常/退货包裹异常等情形）；</div>
    <div>&nbsp; &nbsp; &nbsp; · 不符合退货宝权益保障的其他情形。</div>
  </>,
  },
  Q7: {
    title: <>Q：消费者会收到多少元的运费保障？</>,
    content: <>A：会根据消费者和商家之间的地址进行测算，通常是按照两地间物流首重费用确定保障金额，最终保障的金额以实际抵扣或到账的金额为准。</>,
  },
  Q8: {
    title: <>Q：如何查看单个订单是否可享退货宝权益？</>,
    content: <>A：登录千牛---点击【交易】---查看【已卖出宝贝】---点击【保障详情】。订单显示“保障中”的状态，则该订单可享退货宝权益。</>,
  },
  Q9: {
    title: <>Q：退货宝能保障几次？</>,
    content: <>A：每笔订单（主订单/父订单维度，非子订单维度）仅可享受一次退货宝权益保障。</>,
  },
  Q10: {
    title: <>Q：退货宝保障多久到账?怎么查看保障金额？</>,
    content: <>A：当消费者通过手淘APP等官方退货页发起“上门取件”、“寄件点自寄”退换货时，可自动抵扣首重运费。当消费者选择自行寄回时，需先行支付运费，退换货完成且审核通过，补偿通常在24小时内到账，补偿金额不超过物流首重标准。可前往“订单详情-商品服务-退货宝”页面查看。</>,
  },
  Q11: {
    title: <>Q：换货可以保障吗？</>,
    content: <>A：可以。</>,
  },
  Q12: {
    title: <>Q：退货宝的保障流程是什么？</>,
    content: <>A：消费者购买商品--&gt;商家发货后支付服务费--&gt;消费者退换货完成--&gt;完成退换货运费保障。</>,
  },
  Q13: {
    title: <>Q：针对退货宝被用于套取不当利益的情形，会采取哪些措施？</>,
    content: <>A：会持续不断地全方位提升风险的识别和处罚力度，针对情节严重的会通过法律手段严厉打击，保障商家合法权益。消费者被发现滥用退货宝权益申请运费补偿的，将无法获得补偿。</>,
  },
};

interface CommonQaProps {
  noTitle?: boolean;
  isLogoOverflow?: boolean;
}

const CommonQa = (props: CommonQaProps) => {
  const { noTitle, isLogoOverflow } = props;
  return (
    <div className={styles.qa}>
      {
        !noTitle && <div className={styles['qa-title']}>常见问题</div>
      }
      <div className={styles['qa-content']}>
        {
          Object.keys(qaList).map((item) => {
            return (
              <div className={styles['qa-content-item']}>
                <div className={styles['qa-content-item-q']}>{qaList[item].title}</div>
                <div className={styles['qa-content-item-a']}>{qaList[item].content}</div>
                {qaList[item]?.logoPanel ? (
                  <div className={styles['qa-content-item-extra']}>
                    <ExclusiveLogoPanel isOverflow={isLogoOverflow} />
                  </div>
                ) : null}
              </div>
            );
          })
        }
      </div>
    </div>
  );
};

export default CommonQa;
