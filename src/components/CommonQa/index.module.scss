.qa {
  display: flex;
  flex-direction: column;
  .qa-title {
    display: flex;
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    color: #111;
    margin-bottom: 24px;
    text-align: center;
  }
  .qa-content {
    display: flex;
    flex-direction: column;
    .qa-content-item {
      display: flex;
      flex-direction: column;
      margin-bottom: 18px;
      .qa-content-item-q {
        font-size: 12px;
        font-weight: 600;
        line-height: 24px;
        color: #111;
      }
      .qa-content-item-a {
        font-size: 12px;
        font-weight: normal;
        line-height: 18px;
        letter-spacing: 0;
        color: #666;
      }
    }
  }
}

.qa-mini {
  display: flex;
  flex-direction: column;
  .qa-mini-title {
    margin-bottom: 18px;
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    letter-spacing: 0;
    color: #111;
  }
  .qa-mini-content {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    padding: 18px 12px;
    box-sizing: border-box;
    border: 1px solid #e4e6ed;
    border-radius: 18px;
    font-size: 12px;
    a {
      margin-bottom: 12px;
    }
    .qa-mini-content-more {
      cursor: pointer;
      font-size: 12px;
      color: #3d5eff;
    }
  }
}
