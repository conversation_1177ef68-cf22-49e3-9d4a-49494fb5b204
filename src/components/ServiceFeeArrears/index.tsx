import React from 'react';

import { OFFLINE_PAYMENT_URL, SERVICE_FEE_ARREARS_TEXT } from '@/constant';
import { log } from '@ali/iec-dtao-utils';

import styles from './index.module.scss';

const ServiceFeeArrears = ({
  className = '',
  logKey = '',
  isArrears = false,
}: { className?: string; logKey: string; isArrears: boolean }) => {
  return (
    <>
      {
        isArrears ?
          <div className={[styles['service-fee-arrears'], className].join(' ')}>
            <div className={styles['service-fee-arrears-title']}>服务费欠费</div>
            <div className={styles['service-fee-arrears-content']}>
              您已欠费
              <span
                onClick={() => {
                  window.open(OFFLINE_PAYMENT_URL);
                  log.addLog(logKey, 'click');
                }}
              >
                立即还款
              </span>
            </div>
            <div className={styles['service-fee-arrears-subtitle']}>
              {SERVICE_FEE_ARREARS_TEXT}
            </div>
          </div> : null
      }
    </>
  );
};

export default ServiceFeeArrears;
