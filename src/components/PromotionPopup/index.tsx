import React from 'react';
import styles from './index.module.scss';
import ServiceFeeCalculation from './ServiceFeeCalculation/ServiceFeeCalculation';
import DiscountCoupons from './DiscountCoupons/DiscountCoupons';
import { Balloon } from '@alifd/next';
import { minBy, some, isEmpty } from 'lodash-es';

interface BestOfferProps {
  serviceFeeCalculationProps: ServiceFeeCalculationProps;
  discountCouponsProps: CouponProps[];
  isShowPromotion: boolean;
}
interface ServiceFeeCalculationProps {
  originalFeeAmount: string;
  exemptionAmount: string;
  discountFeeAmount: string;
  feeAmountText: string;
  exemptionAmountText: string;
  discountFeeAmountText: string;
}
interface CouponProps {
  id: string;
  title: string;
  subsidyType: string;
  value?: string;
}

// 定义优先级顺序
const priority = {
  PROMOTION_SUBSIDY: 1,
  BIZ_SUBSIDY: 2,
  PLATFORM_SUBSIDY: 3,
};

const CouponName = {
  PROMOTION_SUBSIDY: '活动优惠',
  BIZ_SUBSIDY: '行业优惠',
  PLATFORM_SUBSIDY: '平台优惠',
};

const BestOffer: React.FC<BestOfferProps> = ({
  serviceFeeCalculationProps,
  discountCouponsProps,
  isShowPromotion,
}) => {
  return (
    <div className={styles.offerBalloon}>
      <ServiceFeeCalculation {...serviceFeeCalculationProps} />
      {
        isShowPromotion &&
        <DiscountCoupons coupons={discountCouponsProps} />
      }
    </div>
  );
};

const PromotionPopup = ({
  promotionDetailList,
  exemptionAmount,
  originalFeeAmount,
  discountFeeAmount,
}) => {
  if (isEmpty(promotionDetailList)) {
    return <></>;
  }

  const highestPriorityPromotion: any = minBy(promotionDetailList, (item) => priority[item?.subsidyType]);
  const priorityName = CouponName[highestPriorityPromotion?.subsidyType];

  const serviceFeeCalculationProps: ServiceFeeCalculationProps = {
    originalFeeAmount,
    discountFeeAmount,
    exemptionAmount,
    discountFeeAmountText: '预计服务费（元）',
    feeAmountText: '应付服务费（元）',
    exemptionAmountText: '优惠减免总金额（元）',
  };

  const isShowPromotion = !some(promotionDetailList, (item) => item.hidden === true);
  const discountCouponsProps = promotionDetailList?.map((item) => {
    const itemData = {
      id: item.promotionId,
      title: item.promotionTitle,
      subsidyType: item?.subsidyType,
      value: item?.promotionAmount ?? null,
    };
    return itemData;
  });

  return (
    <div>
      <Balloon
        v2
        trigger={
          <div id="topRight" className={styles.promotionPopup}>
            <div className={styles.promotionIcon} />
            <div className={styles.promotionTitle}>{priorityName}</div>
          </div>
        }
        align="tl"
        triggerType={['hover', 'click']}
        style={{ width: 400 }}
        closable
        title={'已为您选择最佳优惠'}
      >
        <BestOffer
          isShowPromotion={isShowPromotion}
          serviceFeeCalculationProps={serviceFeeCalculationProps}
          discountCouponsProps={discountCouponsProps}
        />
      </Balloon>
    </div>
  );
};

export default PromotionPopup;
