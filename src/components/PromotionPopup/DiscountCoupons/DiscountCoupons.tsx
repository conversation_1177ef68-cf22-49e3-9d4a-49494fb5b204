import React from 'react';
import styles from './DiscountCoupons.module.scss';
import { money_US } from '@ali/iec-dtao-utils/esm/common/number';
import { lengthOptimization } from '@/utils/tools';

interface DiscountCouponsProps {
  coupons: CouponProps[];
}
interface CouponProps {
  id: string;
  title: string;
  subsidyType: string;
  value?: string;
}

const CouponStyleType = {
  PLATFORM_SUBSIDY: 'couponNormal',
  BIZ_SUBSIDY: 'couponSpecial',
  PROMOTION_SUBSIDY: 'couponSpecial',
};

const DiscountCoupons: React.FC<DiscountCouponsProps> = ({
  coupons,
}) => {
  return (
    <div className={styles.couponContainer}>
      <div className={styles.couponWrapper}>
        {coupons.map((coupon, index) => {
          return (
            <div key={index} className={`${styles[CouponStyleType[coupon.subsidyType]]}`}>
              <span className={styles.discountAmount}>
                减免{money_US(coupon.value) || '-'}元
              </span>
              <span className={styles.couponTitle}>
                {lengthOptimization(coupon.title, 9)}
              </span>
            </div>
          );
        })}
      </div>
      <div className={styles.arrowIndicator}>
        <div />
      </div>
    </div>
  );
};
export default DiscountCoupons;
