.couponContainer {
  display: flex;
  flex-direction: row;
  margin-top: 15px;
  justify-content: flex-end;
  align-self: center;
  position: relative;
  width: 354px;
}

.couponWrapper {
  border-radius: 12px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-self: flex-start;
  box-sizing: border-box;
  padding: 10px;
  border: 1px solid #ffe8dd;
  position: relative;
  background-color: #fff;
  gap: 8px;
}

.couponNormal {
  width: 132px;
  height: 50px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-self: center;
  padding: 4px 6px;
  background-image: url(https://img.alicdn.com/imgextra/i4/6000000007730/O1CN01ok5HtX26yOcrMTOl4_!!6000000007730-2-d2c.png);
  background-repeat: no-repeat;
  background-position: 0 0;
  background-size: 100% 100%;
  background-origin: padding-box;

  .discountAmount {
    color: #fd3f67;
    font-size: 14px;
    text-align: center;
    font-weight: 500;
    line-height: 18px;
    align-self: center;
    max-width: 120px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .couponTitle {
    color: #fd3f67;
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    margin-top: 8px;
    align-self: center;
    white-space: nowrap;
  }
}

.couponSpecial {
  width: 132px;
  height: 50px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-self: center;
  padding: 4px 6px;
  background-image: url(https://img.alicdn.com/imgextra/i4/6000000006781/O1CN01AfETYr1zxkl7SBK3s_!!6000000006781-2-d2c.png);
  background-repeat: no-repeat;
  background-position: 0 0;
  background-size: 100% 100%;
  background-origin: padding-box;

  .discountAmount {
    color: #ff5000;
    font-size: 14px;
    text-align: center;
    font-weight: 500;
    line-height: 18px;
    align-self: center;
    max-width: 120px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .couponTitle {
    color: #999;
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    margin-top: 8px;
    align-self: center;
    white-space: nowrap;
  }
}

.arrowIndicator {
  position: absolute;
  transform: rotate(-45deg);
  width: 12px;
  height: 12px;
  right: 56px;
  top: -6px;
  z-index: 1;
  border-left: none !important;
  border-bottom: none !important;
  box-sizing: content-box !important;
  background-color: #fff;
  border-width: 1px;
  border-color: #ffe8dd;
  border-style: solid;
}
