import React from 'react';
import styles from './ServiceFeeCalculation.module.scss';
import { money_US } from '@ali/iec-dtao-utils/esm/common/number';

interface ServiceFeeCalculationProps {
  originalFeeAmount: string;
  exemptionAmount: string;
  discountFeeAmount: string;
  feeAmountText: string;
  exemptionAmountText: string;
  discountFeeAmountText: string;
}
const ServiceFeeCalculation: React.FC<ServiceFeeCalculationProps> = ({
  originalFeeAmount,
  exemptionAmount,
  discountFeeAmount,
  feeAmountText,
  exemptionAmountText,
  discountFeeAmountText,
}) => {
  return (
    <div className={styles.serviceFeeContainer}>
      <div className={styles.feeItem}>
        <span className={styles.originalFeeAmount}>{money_US(discountFeeAmount) || '-'}</span>
        <span className={styles.feeDescription}>{discountFeeAmountText}</span>
      </div>
      <div className={styles.equalSign}>=</div>
      <div className={styles.feeItem}>
        <div className={styles.feeDetails}>
          <span className={styles.originalFeeAmount}>{money_US(originalFeeAmount) || '-'}</span>
          <span className={styles.feeDescription}>{feeAmountText}</span>
        </div>
      </div>
      <div className={styles.minusSign}>-</div>
      <div className={styles.feeItem}>
        <div className={styles.feeDetails}>
          <span className={styles.originalFeeAmount}>{money_US(exemptionAmount) || '-'}</span>
          <span className={styles.feeDescription}>{exemptionAmountText}</span>
        </div>
      </div>
    </div>
  );
};
export default ServiceFeeCalculation;
