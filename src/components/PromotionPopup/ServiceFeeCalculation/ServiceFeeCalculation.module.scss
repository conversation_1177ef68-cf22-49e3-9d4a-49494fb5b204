.serviceFeeContainer {
  display: flex;
  flex-direction: row;
  margin-top: 10px;
  justify-content: center;
  align-self: center;
  width: 354px;
}

.feeItem {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-self: center;
  height: 38px;

  &:nth-child(2) {
    margin-left: 6px;
    width: 110px;

    .originalFeeAmount {
      margin-left: 1px;
      max-width: 94px;
    }

    .feeDescription {
      color: #919499;
    }
  }

  &:nth-child(3) {
    margin-left: 6px;
    width: 134px;

    .feeDescription {
      color: #919499;
    }
  }
}

.feeDetails {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-self: center;
  height: 38px;
}

.originalFeeAmount {
  color: #494a4d;
  font-size: 16px;
  font-weight: 500;
  line-height: 20px;
  align-self: center;
  max-width: 120px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.feeDescription {
  color: #999;
  font-size: 12px;
  text-align: center;
  font-weight: 400;
  line-height: 16px;
  margin-top: 2px;
  align-self: center;
  white-space: nowrap;
}

.equalSign,
.minusSign {
  color: #494a4d;
  font-size: 12px;
  text-align: center;
  font-weight: 400;
  line-height: 16px;
  align-self: center;
  white-space: nowrap;
}