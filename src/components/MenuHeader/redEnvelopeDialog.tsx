import React, { useState, forwardRef, useImperativeHandle, useEffect } from 'react';
import { Button, Dialog, Loading } from '@alifd/next';
import { log } from '@ali/iec-dtao-utils';
import { ServiceFeeDetails } from '@/api/quit';
import { RED_ENVELOPE_DESC, RED_ENVELOPE_ICON } from '@/constant';
import { money_US } from '@ali/iec-dtao-utils/esm/common/number';

import styles from './index.module.scss';

export interface RedEnvelopeInfoDialogRef {
  trigger: (isOpen: boolean) => void;
}

export interface RedEnvelopeDialogProps {
  loading: boolean;
  onQuit: () => void;
  onReceiveRedEnvelope: () => void;
  redEnvelopeInfo: {
    currentServiceFee: ServiceFeeDetails;
    discountServiceFee: ServiceFeeDetails;
    canNotQuitDays: string;
    effectiveDays: {
      value: string;
    };
    showDiscount: {
      value: string;
    };
  };
}

const RedEnvelopeDialog = (props: RedEnvelopeDialogProps, ref: React.Ref<RedEnvelopeInfoDialogRef>) => {
  const { onReceiveRedEnvelope, onQuit, redEnvelopeInfo, loading = false } = props;
  const { currentServiceFee, discountServiceFee, effectiveDays, showDiscount, canNotQuitDays } = redEnvelopeInfo;

  const [visible, setVisible] = useState(false);

  useImperativeHandle(
    ref,
    () => ({
      trigger: (isOpen: boolean) => setVisible(isOpen),
    }),
    [],
  );

  useEffect(() => {
    visible && log.addLog('red-envelope-dialog-init', 'visit', { redEnvelopeInfo });
  }, [visible]);

  const renderServiceFee = (amount) => {
    if (!String(amount).includes('.')) {
      return (
        <span className={styles['fee-amount']}>
          <span className={styles['integer-amount']}>{amount}</span>
        </span>);
    }

    const [integerAmount, decimalAmount] = String(amount).split('.');

    return (
      <span className={styles['fee-amount']}>
        <span className={styles['integer-amount']}>{integerAmount}</span>.
        <span className={styles['decimal-amount']}>{decimalAmount}</span>
      </span>
    );
  };

  return (
    <Dialog
      v2
      width={700}
      className={styles['red-envelope-dialog']}
      visible={visible}
      footer={false}
      onClose={() => {
        log.addLog('red-envelope-close-icon-click', 'success');
        setVisible(false);
      }}
    >
      <Loading visible={loading} style={{ display: 'block' }} tip="领取中...">
        <div className={styles['red-envelope']}>
          <div className={styles.title}>
            <img src={RED_ENVELOPE_ICON} />
            <span>退出前必领，{effectiveDays || '--'}天退货宝{showDiscount?.value || '--'}折优惠</span>
          </div>
          <div className={styles.desc}>
            若不退出，您可领取{effectiveDays || '--'}天退货宝{showDiscount?.value || '--'}折优惠，领取优惠后{canNotQuitDays || '--'}天内无法退出保障
          </div>
          <div className={styles['content-container']}>
            <div className={styles.content}>
              {/* 您的当前服务费 */}
              <div className={styles['current-service-fee']}>
                <div className={styles.title}>您的当前服务费</div>
                <div className={styles['amount-container']}>
                  <span className={styles.amount}>
                    <span className={styles.symbol}>¥</span>
                    {renderServiceFee(money_US(currentServiceFee?.feeAmount?.value))}<span className={styles.unit}> /单</span>
                    <span className={styles['original-amount']}>¥{money_US(currentServiceFee?.originalFeeAmount?.value)}<span>/单 </span></span>
                  </span>
                </div>
              </div>
              {/* 领取权益后预计的服务费 */}
              <div className={styles['discount-service-fee']}>
                <div className={styles.title}>领取权益后预计的服务费</div>
                <div className={styles['amount-container']}>
                  <span className={styles.amount}>
                    <span className={styles.symbol}>¥</span>
                    {renderServiceFee(money_US(discountServiceFee?.feeAmount?.value))}<span className={styles.unit}> /单</span>
                    <span className={styles['original-amount']}>¥{money_US(discountServiceFee?.originalFeeAmount?.value)} /单</span>
                  </span>
                </div>
              </div>
            </div>
          </div>
          {/* 红包使用说明 */}
          <div className={styles['red-envelope-bottom-desc']}>• {RED_ENVELOPE_DESC}</div>
        </div>
        <div className={styles['red-envelope-footer']}>
          <Button
            type="normal"
            className={styles['receive-btn']}
            onClick={() => {
              log.addLog('red-envelope-receive-click', 'success');
              onReceiveRedEnvelope();
            }}
          >
            立即领取
          </Button>
          <Button
            type="normal"
            className={styles['quit-btn']}
            text
            onClick={() => {
              log.addLog('red-envelope-quit-click', 'success');
              onQuit();
            }}
          >
            确认退出
          </Button>
        </div>
      </Loading>
    </Dialog>
  );
};

export default forwardRef(RedEnvelopeDialog);
