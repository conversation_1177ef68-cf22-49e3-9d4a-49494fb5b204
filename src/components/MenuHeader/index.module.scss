@mixin flex {
  display: flex;
  align-items: center;
}

.un-open-menu {
  @include flex;
  flex-direction: column;
  height: 144px;
  padding: 24px 0 0 0;
  background: linear-gradient(180deg, rgba(61, 94, 255, 0.09) 0%, rgba(61, 94, 255, 0) 100%);

  .open-coupon-btn {
    position: absolute;
    right: 20px;
    font-size: 12px;
    font-weight: normal;
    line-height: 18px;
    text-align: center;
    color: #666;
    cursor: pointer;
  }
  .un-open-menu-head {
    @include flex;
    flex-direction: row;
    margin-top: 42px;

    img {
      width: 492px;
      height: 24px;
    }

    .un-open-menu-head-qa {
      position: absolute;
      right: 50px;
      cursor: pointer;
      font-size: 12px;
      font-weight: normal;
      line-height: 18px;
      text-align: center;
      letter-spacing: 0;
      color: #111;
    }
  }

  .un-open-menu-title {
    width: 252px;
    height: 20px;
    font-size: 14px;
    font-weight: normal;
    line-height: 20px;
    letter-spacing: 0;
    color: #666;
    margin: 12px auto;
  }

}

.opened-menu {
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  .menu-header {
    @include flex;
    flex-direction: row;
    width: 900px;
    height: 32px;

    .menu-header-logo-unopened {
      background-image: url("https://img.alicdn.com/imgextra/i1/O1CN01PIUkCW1z5FZCG8WHG_!!6000000006662-2-tps-426-43.png");
      background-size: 213px 24px;
      width: 213px;
      height: 24px;
      background-repeat: no-repeat;
      margin-right: 12px;
    }

    .menu-header-logo-opened {
      background-image: url("https://img.alicdn.com/imgextra/i3/O1CN01Y8bj2u1yASSDcUWHC_!!6000000006538-2-tps-285-31.png");
      background-size: 142px 16px;
      width: 142px;
      height: 16px;
      background-repeat: no-repeat;
      margin-right: 6px;
    }

    .menu-header-tag {
      @include flex;
      width: 48px;
      height: 18px;
      margin: auto 6px;
      justify-content: center;
    }

    .menu-header-point {
      @include flex;
      flex-direction: row;
      justify-content: center;

      .menu-header-point-opened-icon {
        background-image: url("https://img.alicdn.com/imgextra/i4/O1CN01EcKo0Q1oskbEjfnDx_!!6000000005281-2-tps-28-28.png");
        width: 14px;
        height: 14px;
        background-size: 14px 14px;
        background-repeat: no-repeat;
        margin-right: 6px;
      }

      .menu-header-point-unopened-icon {
        background-image: url("https://img.alicdn.com/imgextra/i1/O1CN01fOQiLN1nRtl3f193h_!!6000000005087-2-tps-28-28.png");
        width: 14px;
        height: 14px;
        background-size: 14px 14px;
        background-repeat: no-repeat;
        margin-right: 6px;
      }

      .menu-header-point-opened-desc {
        font-size: 14px;
        font-weight: normal;
        line-height: 20px;
        margin-right: 18px;
        color: #666;
        white-space: nowrap;
      }

      .menu-header-point-unopened-desc {
        font-size: 14px;
        font-weight: normal;
        line-height: 20px;
        margin-right: 18px;
      }
    }
  }

  .menu-settings {
    display: flex;
    flex-direction: row;

    .menu-settings-qa {
      cursor: pointer !important;
      margin-right: 18px;
      font-size: 14px;
      color: rgb(102, 102, 102);
    }
  }
}

// 意见收集弹窗
.quit-reason-dialog {
  &>:global(.next-dialog-body) {
    padding: 24px !important;
  }

  &> :global(.next-dialog-header) {
    padding-top: 24px;
    padding-bottom: 6px;
    color: #222;
    font-size: 14px;
    font-weight: 600;
  }

  .quit-dialog-container {
    display: flex;
    flex-direction: column;

    .quit-dialog-title {
      color: #222;
      font-size: 14px;
      line-height: 22px;
      font-weight: 600;
      margin-bottom: 6px;
      span {
        color: #999;
        font-weight: normal;
      }
    }

    .quit-dialog-desc {
      margin-bottom: 36px;
      font-size: 12px;
      color: #222;
    }

    .check-box-group {
      margin-left: 20px;
      margin-top: 6px;

      .check-box-desc {
        display: flex;
        flex-direction: column;
        gap: 12px;
        font-family: PingFang SC;
        padding: 9px 12px;
        border: 1px solid #e4e6ed;
        background: #f0f2fa;
        border-radius: 9px;
        color: #111;
        font-size: 12px;
        line-height: 18px;

        :global(.next-input) {
          background: #fff;
          height: 36px;
        }
      }
    }

    :global(.next-checkbox-wrapper) {
      margin-bottom: 18px;
    }

    :global(.next-checkbox-label) {
      display: inline-block;
      width: 90%;
    }
  }

  .quit-dialog-footer {
    text-align: right;
    padding-bottom: 4px;
    margin-top: 10px;
    .cancel-btn {
      margin-right: 12px;
    }
  }
}

// 利益点弹窗
.interest-dialog {
  font-family: PingFang SC;

  &:global(.next-dialog) {
    background-image: url("https://gw.alicdn.com/imgextra/i3/O1CN01cG0uBH1FVqJ0r81U7_!!6000000000493-2-tps-700-480.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  &>:global(.next-dialog-body) {
    padding-right: 24px !important;
  }

  .title {
    font-family: DingTalk JinBuTi;
    margin-top: 54px;
    font-size: 24px;
    font-variation-settings: "opsz" auto;
    background: linear-gradient(94deg, #f27 0%, #e43f63 42%, #ac2249 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 9px;
  }

  .info {
    font-size: 14px;
    line-height: 20px;
    color: #7a8599;
    margin-bottom: 16px;
  }

  .container {
    @include flex;
    justify-content: space-around;
    width: 100%;
    margin-bottom: 12px;

    .item {
      @include flex;
      flex-direction: column;
      justify-content: center;
      width: 209px;
      height: 246px;
      border-radius: 24px;
      background: radial-gradient(35% 42% at 9% 101%, rgba(248, 41, 113, 0.05) 0%, rgba(248, 41, 113, 0) 100%), linear-gradient(90deg, #fff 0%, rgba(255, 255, 255, 0) 100%);

      img {
        width: 161px;
        height: 147px;
      }
    }
  }

  .desc {
    @include flex;
    margin-top: 9px;
    line-height: 24px;

    .dot {
      color: #f52c6f;
      padding-top: 5px;
    }

    .text {
      color: #000;
      font-size: 12px;
      margin: 0 6px;
      padding-top: 3px;
    }

    .exposure {
      font-weight: 600;
      color: #f52c6f;
      font-size: 16px;
    }
  }

  .footer {
    padding-right: 18px;
    text-align: right;
    padding-bottom: 4px;

    .receive-btn {
      margin-right: 30px;
    }
  }
}

// 红包弹窗
.red-envelope-dialog {
  &:global(.next-dialog) {
    background: url("https://gw.alicdn.com/imgextra/i1/O1CN01M5HRse1xh9Av30KsQ_!!6000000006474-2-tps-700-480.png") no-repeat;
    background-size: 100%;
  }

  &>:global(.next-dialog-body) {
    padding-right: 24px !important;
  }

  .red-envelope {
    padding-top: 62px;
    font-family: PingFang SC;

    &>.title {
      @include flex;
      font-family: DingTalk JinBuTi;
      margin-bottom: 13px;
      line-height: 29px;
      margin-left: 6px;
      font-size: 24px;
      font-variation-settings: "opsz" auto;
      background: linear-gradient(95deg, #ff5722 0%, #e4453f 42%, #ac4022 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    &>.desc {
      font-size: 14px;
      line-height: 20px;
      color: #ae4123;
      margin-bottom: 12px;
    }

    .content-container {
      padding: 22px 23px;

      .content {
        @include flex;
        justify-content: space-between;
        width: 100%;
        height: 148px;
        background: url('https://gw.alicdn.com/imgextra/i2/O1CN01RBx7NF21spkMbny9j_!!6000000007041-2-tps-605-148.png') no-repeat;
        background-size: 100% 100%;
        margin-bottom: 12px;

        // 公共样式
        .original-amount {
          font-size: 14px;
          color: #999;
          margin-left: 7px;
          text-decoration: line-through;
        }

        // 当前服务费
        .current-service-fee {
          @include flex;
          font-family: AlibabaFontMd;
          margin-left: 56px;
          flex-direction: column;
          justify-content: center;

          .title {
            font-size: 12px;
            font-weight: 600;
            line-height: 18px;
            background: linear-gradient(90deg, rgba(255, 177, 61, 0) 0%, rgba(255, 177, 61, 0.1) 23%, rgba(255, 177, 61, 0.1) 80%, rgba(255, 177, 61, 0) 100%);
            padding: 3px 36px;
            margin-bottom: 21px;
            color: #111;
          }

          .amount-container {
            line-height: 1;
            color: #7c270f;

            .amount {
              font-family: Alibaba Sans 102;
              font-weight: 600;
              font-size: 30px;
              line-height: 1;

              .symbol {
                font-size: 24px;
              }

              .fee-amount {
                line-height: 1;

                &>span:first-child {
                  font-weight: 500;
                  font-size: 30px;
                }

                &>span:last-child {
                  font-weight: 500;
                  font-size: 24px;
                }
              }

              .unit {
                font-weight: 600;
                font-size: 14px;
              }
            }
          }
        }

        // 领取权益后预计的服务费
        .discount-service-fee {
          font-family: AlibabaFontMd;
          @include flex;
          flex-direction: column;
          justify-content: center;
          margin-right: 40px;

          .title {
            margin-bottom: 19px;
            font-size: 12px;
            font-weight: 600;
            line-height: 18px;
            color: #111;
            padding: 3px 36px;
            background: linear-gradient(90deg, rgba(255, 103, 61, 0) 0%, rgba(255, 103, 61, 0.2) 23%, rgba(255, 103, 61, 0.1) 80%, rgba(255, 103, 61, 0) 100%);
          }

          .amount-container {
            line-height: 1;
            font-family: Alibaba Sans 102;

            .amount {
              color: #ff2f39;
              font-weight: 600;
              margin-right: 7px;
              font-size: 42px;

              .symbol {
                font-size: 36px;
              }

              .fee-amount {
                line-height: 1;

                .integer-amount {
                  font-weight: 500;
                  font-size: 42px;
                }

                .decimal-amount {
                  font-weight: 500;
                  font-size: 28px;
                }
              }

              .unit {
                font-weight: 600;
                font-size: 18px;
              }
            }
          }
        }
      }
    }

    &-bottom-desc {
      font-size: 12px;
      line-height: 18px;
      color: #7c270f;
      margin-bottom: 46px;
    }
  }

  .red-envelope-footer {
    padding-right: 18px;
    text-align: right;
    padding-bottom: 4px;

    .receive-btn {
      margin-right: 30px;
      color: #fff;
      background: linear-gradient(247deg, #ff912b 0%, #ff5a5a 100%, #ff4544 100%);
      border-color: #ff912b;

      &:hover {
        background: linear-gradient(247deg, #ff912b 0%, #ff5a5a 100%, #ff4544 100%);
        color: #fff;
        border-color: #ff912b;
      }
    }
  }
}

// 领取红包结果弹窗
.receive-result-dialog {
  font-family: PingFang SC;

  &>:global(.next-dialog-body) {
    padding-right: 24px !important;
    padding-bottom: 18px;
  }

  .receive-result-content {
    padding: 54px 0 0;

    .title {
      @include flex;
      margin-bottom: 6px;
      line-height: 24px;

      img {
        width: 24px;
        height: 24px;
        margin-right: 12px;
      }

      span {
        font-size: 16px;
        color: #111;
      }
    }

    .desc {
      font-size: 14px;
      line-height: 20px;
      color: #666;
      padding: 0 36px;
      margin-bottom: 16px;
    }
  }

  .receive-result-footer {
    padding: 6px 0;
    text-align: right;

    .cancel-btn {
      &:global(.next-btn) {
        height: 36px;
        margin-left: 12px;
        padding: 10px 18px;
        background: #f0f2fa;
        border-radius: 100px;
      }
    }
  }
}