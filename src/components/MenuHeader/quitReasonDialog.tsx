import React, { useState, forwardRef, useImperativeHandle, useEffect } from 'react';
import { Dialog, Checkbox, Input, Loading, Message, Button } from '@alifd/next';
import { log } from '@ali/iec-dtao-utils';
import { QUIT_REASON_LIST } from '@/constant';
import { map, includes } from 'lodash-es';

import styles from './index.module.scss';

export interface QuitReasonDialogRef {
  trigger: (isOpen: boolean) => void;
}

export interface QuitReasonDialogProps {
  onChangeReason: (value: string[]) => void;
  onQuitBtn: (params: string[]) => void;
  loading: boolean;
}

const QuitReasonDialog = (props: QuitReasonDialogProps, ref: React.Ref<QuitReasonDialogRef>) => {
  const { onChangeReason, onQuitBtn = () => { }, loading = false } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const [quitReasonList, setQuitReasonList] = useState<string[]>([]);
  const [inputValue, setInputValue] = useState({});

  useImperativeHandle(
    ref,
    () => ({
      trigger: (isOpen: boolean) => setVisible(isOpen),
    }),
    [],
  );

  useEffect(() => {
    setQuitReasonList([]);
    setInputValue([]);
    visible && log.addLog('quit-reason-dialog-init', 'visit');
  }, [visible]);

  return (
    <Dialog
      v2
      visible={visible}
      width={500}
      className={styles['quit-reason-dialog']}
      footer={false}
      onClose={() => {
        log.addLog('quit-reason-close-click', 'success');
        setVisible(false);
      }}
    >
      <Loading visible={loading} style={{ display: 'block' }} tip="退出中...">
        <div className={styles['quit-dialog-container']}>
          <div className={styles['quit-dialog-title']}>您的建议，让我们做的更好 <span>（可多选）</span></div>
          <div className={styles['quit-dialog-desc']}>1分钟反馈，助力服务全面升级</div>
          <Checkbox.Group
            direction={'ver'}
            onChange={(checkedList: string[]) => {
              log.addLog('quit-reason-select-item', 'success');
              setQuitReasonList(checkedList);
              onChangeReason(checkedList);
            }}
          >
            {
              map(QUIT_REASON_LIST, (item, index) =>
                (
                  <Checkbox
                    label={item.label}
                    value={item.value}
                    onClick={() => {
                      // 删除对应的input值
                      if (inputValue[index]) {
                        delete inputValue[index];
                      }
                    }}
                  >
                    {
                    includes(quitReasonList, item.value) && (item?.desc || item?.isInput) ?
                      <div className={styles['check-box-group']}>
                        <div className={styles['check-box-desc']}>
                          {item?.desc && <div className={styles['check-box-desc-text']}>{item.desc}</div>}
                          {item?.isInput &&
                            <Input
                              onChange={(value) => {
                                inputValue[index] = value;
                                if (!value) {
                                  delete inputValue[index];
                                }
                                setInputValue({ ...inputValue });
                              }}
                              showLimitHint
                              size="small"
                              style={{ width: '100%' }}
                              placeholder="请输入"
                              maxLength={50}
                            />
                          }
                        </div>
                      </div> : null
                  }
                  </Checkbox>
                ))
            }
          </Checkbox.Group>
          <div className={styles['quit-dialog-footer']}>
            <Button
              type="primary"
              className={styles['cancel-btn']}
              onClick={() => {
                log.addLog('quit-reason-cancel-click', 'success');
                setVisible(false);
              }}
            >
              我再想想
            </Button>
            <Button
              type="normal"
              onClick={() => {
                if (!quitReasonList?.length) {
                  Message.error('请选择退出原因');
                  return;
                }
                log.addLog('quit-reason-ok-click', 'success');
                onQuitBtn(Object.values(inputValue));
              }}
            >
              确认退出
            </Button>
          </div>
        </div>
      </Loading>
    </Dialog>
  );
};

export default forwardRef(QuitReasonDialog);
