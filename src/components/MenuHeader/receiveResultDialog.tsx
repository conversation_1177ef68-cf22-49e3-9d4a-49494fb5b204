import React, { useState, forwardRef, useImperativeHandle, useEffect } from 'react';
import { Button, Dialog } from '@alifd/next';
import { log } from '@ali/iec-dtao-utils';
import { includes } from 'lodash-es';

import styles from './index.module.scss';

export interface ReceiveResultDialogRef {
  trigger: (isOpen: boolean) => void;
}
export interface ReceiveProps {
  onConfirm: (params: string) => void;
  receiveInfo: {
    iconPath: string;
    title: string;
    desc: string;
    code: string;
  };
}

const ReceiveResultDialog = (props: ReceiveProps, ref: React.Ref<ReceiveResultDialogRef>) => {
  const { receiveInfo, onConfirm = () => { } } = props;
  const [visible, setVisible] = useState(false);

  useImperativeHandle(
    ref,
    () => ({
      trigger: (isOpen: boolean) => setVisible(isOpen),
    }),
    [],
  );

  useEffect(() => {
    visible && log.addLog('receive-result-dialog-init', 'visit', { code: receiveInfo?.code });
  }, [visible]);

  return (
    <Dialog
      v2
      className={styles['receive-result-dialog']}
      visible={visible}
      width={420}
      footer={false}
      onClose={() => {
        log.addLog('receive-result-dialog-close-click', 'success', { code: receiveInfo?.code });
        setVisible(false);
        receiveInfo?.code === 'success' && window.location.reload();
      }}
    >
      <div className={styles['receive-result-content']}>
        <div className={styles.title}>
          <img src={receiveInfo?.iconPath} />
          <span>{receiveInfo?.title}</span>
        </div>
        <div className={styles.desc}>
          {receiveInfo?.desc}
        </div>
      </div>
      <div className={styles['receive-result-footer']}>
        <Button
          type="primary"
          onClick={() => {
            log.addLog('receive-result-dialog-ok-click', 'success', { code: receiveInfo?.code });
            onConfirm(receiveInfo?.code);
          }}
        >
          {receiveInfo?.code === 'success' ? '我知道了' : '确认'}
        </Button>
        {!includes(['success'], receiveInfo?.code) &&
          <Button
            type="normal"
            text
            className={styles['cancel-btn']}
            onClick={() => {
              log.addLog('receive-result-dialog-cancel-click', 'success', { code: receiveInfo?.code });
              setVisible(false);
            }}
          >
            取消
          </Button>
        }
      </div>
    </Dialog>
  );
};

export default forwardRef(ReceiveResultDialog);
