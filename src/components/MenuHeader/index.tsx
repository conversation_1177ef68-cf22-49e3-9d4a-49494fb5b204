import React, { useState, useRef } from 'react';
import dayjs from 'dayjs';
import { includes, get } from 'lodash-es';
import { HEADER_LOGO, MERCHANT_FROZEN, MERCHANT_OPEN_FAILED, MERCHANT_OPENING, MERCHANT_QUIT_SUCCEED, MERCHANT_QUITTING, QIANNIU_PC, RECEIVE_RESULT_ERROR_INFO, RECEIVE_RESULT_SUCCESS_INFO, STATUS_INFO } from '@/constant';
import { Button, Dialog, Drawer, Dropdown, Menu, Message } from '@alifd/next';
import { log } from '@ali/iec-dtao-utils';
import { animationDelay } from '@/utils/tools';
import NewPointPanel from '../NewPointPanel';
import PartSetting from '../PartSetting';
import { getCookie } from '@/utils/cookie';
import { changePartSetting } from '@/api/open';
import CouponOpenDrawer from '../CouponOpenDrawer';
import { queryBatchTrail, quitAdmit, receiveRedEnvelope } from '@/api/quit';
import InterestDialog, { InterestDialogRef } from './interestDialog';
import RedEnvelopeDialog, { RedEnvelopeInfoDialogRef } from './redEnvelopeDialog';
import QuitReasonDialog, { QuitReasonDialogRef } from './quitReasonDialog';
import ReceiveResultDialog, { ReceiveResultDialogRef } from './receiveResultDialog';

import styles from './index.module.scss';

interface MenuHeaderProps {
  status: string;
  quitDialogLoading?: boolean;
  isOpenFixed?: boolean;
  showSettings?: boolean;
  partAdmit?: boolean;
  giveType?: string | null;
  onQuit?: (quitReasonList: string[], quitReasonDialogRef: any) => void;
  onShowQa?: () => void;
  onShowProtocol?: () => void;
}

const POINT_LIST = ['提升到店流量', '提升订单转化', '降低售后纠纷', '享退货宝专属标识'];

const MenuHeader = (props: MenuHeaderProps) => {
  const { status, showSettings = false, isOpenFixed = false, giveType, partAdmit, quitDialogLoading = false } = props;
  const isNotOpen = includes(['null', MERCHANT_OPENING, MERCHANT_OPEN_FAILED, MERCHANT_QUIT_SUCCEED], status);
  const [isShowCouponOpenDrawer, setIsShowCouponOpenDrawer] = useState(false);

  const [quitReasonList, setQuitReasonList] = useState<string[]>([]);
  const [showPartSetting, setShowPartSetting] = useState<boolean>(false);

  const [redEnvelopeLoading, setRedEnvelopeLoading] = useState<boolean>(false);
  const [batchTrailLoading, setBatchTrailLoading] = useState<boolean>(false);

  const [quitRetainDialogType, setQuitRetainDialogType] = useState<string>('');
  const [redEnvelopeInfo, setRedEnvelopeInfo] = useState<any>({});
  const [receiveInfo, setReceiveInfo] = useState<any>({});

  const interestDialogRef = useRef<InterestDialogRef>(null);
  const redEnvelopeDialogRef = useRef<RedEnvelopeInfoDialogRef>(null);
  const quitReasonDialogRef = useRef<QuitReasonDialogRef>(null);
  const receiveResultDialogRef = useRef<ReceiveResultDialogRef>(null);

  const handleOnQuit = async () => {
    if (isOpenFixed) {
      Dialog.warning({
        width: 420,
        v2: true,
        content: '对不起，您已经参加了承诺参与固定天数的活动，活动有效期内无法退出。',
      });
      return;
    }
    try {
      const data = await quitAdmit({ channel: QIANNIU_PC });
      const { success, isAdmit, attributes } = data;
      const { quitRetainPopupType, rejectRuleList } = attributes || {};
      if (!success) {
        log.addLog('quit-pre-admit-request-fail', 'error', { error: 'SYSTEM_ERROR' });
        Message.error('系统异常,请稍后再试');
        return;
      }

      if (!isAdmit) {
        log.addLog('quit-pre-admit-reject-dialog-init', 'visit');
        const { name, beginTime, endTime } = get(rejectRuleList, '[0]');
        const checkTime = (time, formatRule) => (time && formatRule ? dayjs(time).format(formatRule) : '--');
        Dialog.warning({
          v2: true,
          title: '退出失败',
          content: `您于${checkTime(beginTime, 'YYYY-MM-DD')}参与${name || '--'}活动享受专属优惠，${checkTime(endTime, 'YYYY-MM-DD HH:mm:ss')} 前无法退出，感谢您的理解`,
          onOk: () => {
            log.addLog('quit-pre-admit-reject-dialog-ok-click', 'success');
          },
        });
        return;
      }
      setQuitRetainDialogType(quitRetainPopupType);
      interestDialogRef.current?.trigger(true);
    } catch (error) {
      log.addLog('quit-pre-admit-request-fail', 'error', { error: 'SYSTEM_ERROR' });
    }
  };

  // 红包试算
  const queryBatchTrailRequest = async () => {
    try {
      setBatchTrailLoading(true);
      const batchTrailRes = await queryBatchTrail({ channel: QIANNIU_PC });
      const { success, canSendCoupon, serviceFeeDetails, attributes } = batchTrailRes || {};
      const { currentServiceFee, discountServiceFee } = serviceFeeDetails || {};
      const { discount, showDiscount, effectiveDays, canNotQuitDays } = attributes || {};

      setBatchTrailLoading(false);
      interestDialogRef.current?.trigger(false);
      await animationDelay();

      if (!success) {
        log.addLog('query-trail-fail', 'error', { error: batchTrailRes });
        quitReasonDialogRef.current?.trigger(true);
        return;
      }

      if (!canSendCoupon) {
        log.addLog('query-trail-no-info', 'error', { error: batchTrailRes });
        quitReasonDialogRef.current?.trigger(true);
        return;
      }
      setRedEnvelopeInfo({ currentServiceFee, discountServiceFee, showDiscount, effectiveDays, canNotQuitDays, discount });
      redEnvelopeDialogRef.current?.trigger(true);
    } catch (error) {
      setBatchTrailLoading(false);
      interestDialogRef.current?.trigger(false);
      await animationDelay();
      quitReasonDialogRef.current?.trigger(true);
      log.addLog('query-trail-fail', 'error', { error: 'QUERY-BATCH-TRAIL-FAIL' });
    }
  };

  // 未开通
  if (isNotOpen) {
    return (
      <div className={styles['un-open-menu']}>
        <div
          className={styles['open-coupon-btn']}
          onClick={() => setIsShowCouponOpenDrawer(true)}
        >我的权益
        </div>
        <NewPointPanel />
        <div className={styles['un-open-menu-head']}>
          <img src={HEADER_LOGO} />
        </div>
        <Drawer
          title="我的权益"
          placement="right"
          visible={isShowCouponOpenDrawer}
          width={810}
          onClose={() => { setIsShowCouponOpenDrawer(false); }}
        >
          <CouponOpenDrawer />
        </Drawer>
      </div>
    );
  }

  const handleOnPartChange = async (value) => {
    Message.loading('正在切换服务设置，请稍后');
    try {
      const params = {
        channel: QIANNIU_PC,
        identifier: `${getCookie()}-${Math.floor(Date.now())}`,
        giveType: value,
      };
      const res = await changePartSetting(params);
      const { responseCode } = res;
      if (includes(['SUCCESS', 'USER_OPERATION_SUCCEED'], responseCode)) {
        Message.success('设置成功');
        window.location.reload();
        return;
      }
      throw new Error();
    } catch (error) {
      Message.error('切换失败，请稍后再试');
    }
  };

  // 确认退出
  const handleRedEnvelopeQuitBtn = async () => {
    redEnvelopeDialogRef?.current?.trigger(false);
    await animationDelay();
    quitReasonDialogRef.current?.trigger(true);
  };

  // 立即领取
  const handleReceiveRedEnvelope = async () => {
    try {
      setRedEnvelopeLoading(true);
      const { discount, showDiscount, effectiveDays } = redEnvelopeInfo;
      const res = await receiveRedEnvelope({
        identifier: `${getCookie()}-${Math.floor(Date.now())}`,
        expectDiscount: discount?.value,
        expectShowDiscount: showDiscount?.value,
        expectEffectiveDays: effectiveDays,
      });

      const { success, sendCoupon, attributes, responseCode } = res;

      setRedEnvelopeLoading(false);
      redEnvelopeDialogRef?.current?.trigger(false);

      if (!success) {
        log.addLog('receive-red-envelope-fail', 'error', { errorCode: 'SYSTEM_ERROR' });
        Message.error('系统异常, 请稍后再试');
        return;
      }

      if (includes(['DYNAMIC_PARAM_INVALID'], responseCode)) {
        log.addLog('receive-red-envelope-fail', 'error', { errorCode: 'DYNAMIC_PARAM_INVALID' });
        Message.error('数据更新，请稍候');
        return;
      }

      // 领取失败
      if (!sendCoupon) {
        const errorCode = get(attributes, 'errorCode');
        log.addLog('receive-red-envelope-fail', 'error', { errorCode });

        if (includes(['ERROR-8888'], errorCode)) {
          Message.error('抱歉，当前系统异常，稍后您可尝试重新领取');
          return;
        }
        const messageData = RECEIVE_RESULT_ERROR_INFO[errorCode] || RECEIVE_RESULT_ERROR_INFO.default;
        setReceiveInfo(messageData);
        await animationDelay();
        receiveResultDialogRef.current?.trigger(true);
        return;
      }

      // 领取成功
      log.addLog('receive-red-envelope-success', 'success');
      setReceiveInfo(RECEIVE_RESULT_SUCCESS_INFO);
      await animationDelay();
      receiveResultDialogRef.current?.trigger(true);
    } catch (error) {
      log.addLog('receive-red-envelope-fail', 'error', { error: 'SYSTEM_ERROR' });
      setRedEnvelopeLoading(false);
      redEnvelopeDialogRef?.current?.trigger(false);
      Message.error(error?.message);
    }
  };

  const handleReceiveResultBtn = async (code) => {
    switch (code) {
      case 'success': // 领取成功
        receiveResultDialogRef?.current?.trigger(false);
        window.location.reload();
        break;
      case 'ERROR-2001': // 超出单人发放疲惫度
      case 'ERROR-4017': // 超出总体发放疲惫度
      default: // 不可重试其他异常
        receiveResultDialogRef?.current?.trigger(false);
        await animationDelay();
        quitReasonDialogRef?.current?.trigger(true);
        break;
    }
  };

  return (
    <div className={styles['opened-menu']}>
      <div className={styles['menu-header']}>
        <div className={styles['menu-header-logo-opened']} />
        <div
          className={styles['menu-header-tag']}
          style={{
            backgroundColor: STATUS_INFO[status].bgColor,
            color: STATUS_INFO[status].color,
          }}
        >
          {STATUS_INFO[status].title}
        </div>
        <div className={styles['menu-header-point']}>
          {
            POINT_LIST.map((item) => {
              return (
                <>
                  <div className={styles['menu-header-point-opened-icon']} />
                  <div className={styles['menu-header-point-opened-desc']}>{item}</div>
                </>
              );
            })
          }
        </div>
        <div className={styles['menu-header-point']} />
      </div>
      <div className={styles['menu-settings']}>
        <div
          className={styles['menu-settings-qa']}
          onClick={() => {
            window.open('/home.htm/assets-byf-service/coupon', '_blank');
          }}
        >我的权益
        </div>
        {showSettings &&
          <Dropdown
            trigger={<Button text style={{ color: '#666' }}>更多</Button>}
            triggerType={['hover', 'click']}
          >
            <Menu>
              {
                partAdmit &&
                <Menu.Item onClick={() => {
                  log.addLog('part-setting-menu-click', 'success');
                  setShowPartSetting(true);
                }}
                >服务设置
                </Menu.Item>
              }
              <Menu.Item onClick={
                () => {
                  props?.onShowProtocol && props?.onShowProtocol();
                }}
              >相关协议
              </Menu.Item>
              <Menu.Item onClick={() => {
                props?.onShowQa && props?.onShowQa();
              }}
              >常见问题
              </Menu.Item>
              <Menu.Item
                disabled={includes([MERCHANT_QUITTING, MERCHANT_FROZEN], status)}
                onClick={() => {
                  log.addLog('quit-menu-click', 'success');
                  handleOnQuit();
                }}
              >退出服务
              </Menu.Item>
            </Menu>
          </Dropdown>
        }
      </div>
      {/* 利益点弹窗 */}
      <InterestDialog
        ref={interestDialogRef}
        loading={batchTrailLoading}
        quitRetainDialogType={quitRetainDialogType}
        onQuit={queryBatchTrailRequest}
      />
      {/* 红包弹窗 */}
      <RedEnvelopeDialog
        ref={redEnvelopeDialogRef}
        loading={redEnvelopeLoading}
        redEnvelopeInfo={redEnvelopeInfo}
        onReceiveRedEnvelope={handleReceiveRedEnvelope}
        onQuit={handleRedEnvelopeQuitBtn}
      />
      {/* 红包领取结果弹窗 */}
      <ReceiveResultDialog
        ref={receiveResultDialogRef}
        receiveInfo={receiveInfo}
        onConfirm={(code) => handleReceiveResultBtn(code)}
      />
      {/* 意见收集弹窗 */}
      <QuitReasonDialog
        ref={quitReasonDialogRef}
        loading={quitDialogLoading}
        onChangeReason={setQuitReasonList}
        onQuitBtn={(inputValues: string[]) => {
          const quitReason = quitReasonList.concat(inputValues);
          props?.onQuit && props.onQuit(quitReason, quitReasonDialogRef);
        }}
      />
      {/* 服务设置组件 */}
      <PartSetting
        isShow={showPartSetting}
        giveType={giveType}
        setIsShow={(isShow) => setShowPartSetting(isShow)}
        onChangeSetting={handleOnPartChange}
      />
    </div>
  );
};

export default MenuHeader;
