import React, { useState, forwardRef, useImperativeHandle, useEffect } from 'react';
import { Button, Dialog, Loading } from '@alifd/next';
import { log } from '@ali/iec-dtao-utils';

import { INTEREST_DISPUTE_INFO, INTEREST_LIST } from '@/constant';

import styles from './index.module.scss';

export interface InterestDialogRef {
  trigger: (isOpen: boolean) => void;
}
export interface InterestProps {
  loading: boolean;
  quitRetainDialogType: string;
  onQuit: () => void;
}

const retainType = {
  forward: '+',
  backward: '-',
};

const InterestDialog = (props: InterestProps, ref: React.Ref<InterestDialogRef>) => {
  const { quitRetainDialogType, loading = false, onQuit = () => { } } = props;
  const [visible, setVisible] = useState(false);

  useImperativeHandle(
    ref,
    () => ({
      trigger: (isOpen: boolean) => setVisible(isOpen),
    }),
    [],
  );

  useEffect(() => {
    visible && log.addLog('interest-dialog-init', 'visit', { quitRetainDialogType });
  }, [visible]);

  return (
    <Dialog
      v2
      width={700}
      className={styles['interest-dialog']}
      visible={visible}
      footer={false}
      onClose={() => {
        log.addLog('interest-dialog-close-click', 'success', { quitRetainDialogType });
        setVisible(false);
      }}
    >
      <Loading visible={loading} style={{ display: 'block' }} tip="退出中...">
        <div className={styles.title}>您确定退出消费者体验提升计划吗？</div>
        <div className={styles.info}>退出后，历史订单权益不受影响，新增订单将取消专属标识及运费保障权益。若您后续参与平台大促活动，可能影响活动准入资格。</div>
        <div className={styles.container}>
          {
            INTEREST_LIST?.map((item) => {
              return (
                <div className={styles.item}>
                  <img src={item.path} />
                  <div className={styles.desc}>
                    <span className={styles.dot}>•</span>
                    <span className={styles.text}>{item.text}</span>
                    <span className={styles.exposure}>{retainType[quitRetainDialogType]}{item.exposure || '-'}%</span>
                  </div>
                </div>
              );
            })
          }
          <div className={styles.item}>
            <img src={quitRetainDialogType === 'forward' ? INTEREST_DISPUTE_INFO.decPath : INTEREST_DISPUTE_INFO.risePath} />
            <div className={styles.desc}>
              <span className={styles.dot}>•</span>
              <span className={styles.text}>{INTEREST_DISPUTE_INFO.text}</span>
              <span className={styles.exposure}>
                <span>{quitRetainDialogType === 'forward' ? '-' : '+'}</span>
                {INTEREST_DISPUTE_INFO.exposure}%
              </span>
            </div>
          </div>
        </div>
        <div className={styles.footer}>
          <Button
            type="primary"
            className={styles['receive-btn']}
            onClick={() => {
              log.addLog('interest-cancel-click', 'success', { quitRetainDialogType });
              setVisible(false);
            }}
          >
            我再想想
          </Button>
          <Button
            type="secondary"
            text
            onClick={() => {
              log.addLog('interest-dialog-ok-click', 'success', { quitRetainDialogType });
              onQuit();
            }}
          >
            确认退出
          </Button>
        </div>
      </Loading>
    </Dialog>
  );
};

export default forwardRef(InterestDialog);
