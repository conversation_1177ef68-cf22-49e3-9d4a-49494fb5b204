.couponContainer {
  display: flex;
  flex-direction: row;
  margin-top: 15px;
  justify-content: flex-start;
  align-self: center;
  position: relative;
  width: 354px;
}

.couponWrapper {
  border-radius: 12px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  justify-content: center;
  align-self: flex-start;
  box-sizing: border-box;
  padding: 10px;
  border: 1px solid #ffe8dd;
  position: relative;
  background-color: #fff;
}

.arrowIndicator {
  position: absolute;
  transform: rotate(-45deg);
  width: 12px;
  height: 12px;
  left: 36px;
  top: -6px;
  z-index: 1;
  border-left: none !important;
  border-bottom: none !important;
  box-sizing: content-box !important;
  background-color: #fff;
  border-width: 1px;
  border-color: #ffe8dd;
  border-style: solid;
}

.itemWrapper {
  display: flex;
  flex-direction: column;
  width: 167px;
  height: 78px;

  .couponTag {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 67px;
    height: 18px;
    border-radius: 10px 10px 10px 0;
    justify-content: center;
    z-index: 2;

    span {
      font-size: 12px;
      font-weight: normal;
      line-height: 12px;
      color: #fff;
    }
  }

  .detailCoupon {
    display: flex;
    flex-direction: column;
    width: 167px;
    height: 62px;
    background-size: 167px 62px;
    margin-top: -5px;

    .detailCouponPanel {
      display: flex;
      flex-direction: row;
      width: 167px;
      height: 43px;
      padding: 0 10px;

      .detailCouponPanelPrice {
        display: flex;
        height: 43px;
        align-items: center;
        font-size: 16px;
        font-weight: 500;
        line-height: normal;
        text-align: center;

        span {
          margin-left: 2px;
          font-size: 12px;
        }
      }

      .detailCouponPanelSplit {
        display: flex;
        height: 43px;
        margin: 0 5px;
        align-items: center;
        width: 1px;
        height: 40px;
        border: 0.5px dashed;
        opacity: 0.1;
      }

      .detailCouponPanelTitle {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        font-weight: normal;
        line-height: normal;
      }
    }

    .detailCouponExtra {
      display: flex;
      height: 20px;
      justify-content: center;
      align-items: center;

      .detailCouponExtraText {
        display: flex;
        justify-content: center;
        font-size: 12px;
        font-weight: 500;
        line-height: 14px;
        text-align: center;
        letter-spacing: 0;
      }
    }
  }
}
