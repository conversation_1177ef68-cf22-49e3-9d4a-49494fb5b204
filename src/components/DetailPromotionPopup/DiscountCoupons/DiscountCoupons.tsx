import React from 'react';
import styles from './DiscountCoupons.module.scss';
import { money_US } from '@ali/iec-dtao-utils/esm/common/number';
import { renderPrice } from '@/utils/tools';

interface DiscountCouponsProps {
  coupons: CouponProps[];
}

interface CouponProps {
  id: string;
  title: string;
  subsidyType: string;
  type: string;
  discount?: string | null;
  fixPromotion?: string | null;
  hidden?: boolean | null;
  promotionAmount?: any;
}

const COUPON_NAME = {
  PROMOTION_SUBSIDY: '活动优惠',
  BIZ_SUBSIDY: '行业优惠',
  PLATFORM_SUBSIDY: '平台优惠',
};

const ACTIVITY_BG = 'https://img.alicdn.com/imgextra/i3/O1CN01z9xdAb1SGoJ6n3KMs_!!6000000002220-2-tps-334-122.png';
const NORMAL_BG = 'https://img.alicdn.com/imgextra/i2/O1CN013yFN1d1h7qnPvFeKC_!!6000000004231-2-tps-334-122.png';

const COUPON_ITEM_BG = {
  PLATFORM_SUBSIDY: {
    backgroundImage: `url(${NORMAL_BG})`,
    color: '#FF5000',
    tagColor: '#FF5000',
  },
  BIZ_SUBSIDY: {
    backgroundImage: `url(${ACTIVITY_BG})`,
    color: '#FF5000',
    tagColor: 'linear-gradient(90deg, #ff790b 0%, #ff2ab6 100%)',
  },
  PROMOTION_SUBSIDY: {
    backgroundImage: `url(${ACTIVITY_BG})`,
    color: '#FF5000',
    tagColor: 'linear-gradient(90deg, #ff790b 0%, #ff2ab6 100%)',
  },
};

const renderTitle = (title) => {
  if (title && title.length > 6) {
    return `${title.slice(0, 6)}...`;
  }
  return title;
};


const DetailCouponItem = ({
  title,
  subsidyType,
  type,
  discount,
  fixPromotion,
  promotionAmount,
}) => {
  const price = {
    type,
    discount,
    fixPromotion,
  };
  return (
    <div className={styles.itemWrapper} >
      <div className={styles.couponTag} style={{ background: COUPON_ITEM_BG[subsidyType]?.tagColor }}>
        <span>{COUPON_NAME[subsidyType]}</span>
      </div>
      <div
        className={styles.detailCoupon}
        style={{
          backgroundImage: COUPON_ITEM_BG[subsidyType]?.backgroundImage,
          color: COUPON_ITEM_BG[subsidyType]?.color,
        }}
      >
        <div className={styles.detailCouponPanel}>
          <div className={styles.detailCouponPanelPrice}>
            {
              renderPrice(price).priceText ? `${renderPrice(price).priceText}` : null
            }
            {
              renderPrice(price).priceUnit ?
                (
                  <span>{`${renderPrice(price).priceUnit}`}</span>
                ) : null
            }
          </div>
          <div
            className={styles.detailCouponPanelSplit}
            style={{ borderImage: COUPON_ITEM_BG[subsidyType]?.color }}
          />
          <div className={styles.detailCouponPanelTitle}>
            {renderTitle(title)}
          </div>
        </div>
        <div className={styles.detailCouponExtra}>
          <div className={styles.detailCouponExtraText}>
            减免{money_US(promotionAmount?.value) || '-'}元
          </div>
        </div>
      </div>
    </div>
  );
};

const DiscountCoupons: React.FC<DiscountCouponsProps> = ({
  coupons,
}) => {
  return (
    <div className={styles.couponContainer}>
      <div className={styles.couponWrapper}>
        {
          coupons.map((coupon, index) => {
            return (
              <DetailCouponItem
                key={index}
                title={coupon.title}
                subsidyType={coupon.subsidyType}
                type={coupon?.type}
                discount={coupon?.discount}
                fixPromotion={coupon?.fixPromotion}
                promotionAmount={coupon?.promotionAmount}
              />
            );
          })
        }
      </div>
      <div className={styles.arrowIndicator} />
    </div>
  );
};
export default DiscountCoupons;
