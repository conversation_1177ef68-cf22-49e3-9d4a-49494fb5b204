import React from 'react';
import DiscountCoupons from './DiscountCoupons/DiscountCoupons';

const DetailPromotionPopup = ({ promotionDetailList }) => {
  const discountCouponsProps = promotionDetailList?.map((item) => {
    const itemData = {
      id: item.id,
      title: item.title,
      subsidyType: item?.subsidyType,
      type: item?.type,
      discount: item?.discount,
      fixPromotion: item?.fixPromotion,
      hidden: item?.hidden,
      promotionAmount: item?.promotionAmount,
    };
    return itemData;
  });

  return (
    <DiscountCoupons
      coupons={discountCouponsProps}
    />
  );
};

export default DetailPromotionPopup;

