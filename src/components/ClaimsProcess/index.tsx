import React from 'react';
import styles from './index.module.scss';

const CLAIMS_PROCESS_LIST = ['消费者购买商品', '商家发货后支付服务费', '消费者退换货完成', '运费保障'];

const ClaimsProcess = () => {
  return (
    <div className={styles['claims-process']}>
      <div className={styles.title}>运费保障流程</div>
      <div className={styles.process}>
        {
          CLAIMS_PROCESS_LIST.map((item, idx) => {
            return (
              <>
                <div className={styles['process-item']}>{idx + 1}.{item}</div>
                {
                  idx !== CLAIMS_PROCESS_LIST.length - 1 &&
                  <div className={styles['process-icon']} />
                }
              </>
            );
          })
        }
      </div>
    </div>
  );
};

export default ClaimsProcess;
