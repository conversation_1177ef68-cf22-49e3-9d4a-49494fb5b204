import { isEmpty, isString, maxBy } from 'lodash-es';
import { log } from '@ali/iec-dtao-utils';
import { money_US } from '@ali/iec-dtao-utils/esm/common/number';

const compareAmountToolMethod = (action, a, b) => {
  if (isEmpty(a) || isEmpty(b)) {
    log.addLog(action, 'error', { pre: a, next: b });
    return false;
  }

  return Number(a) !== Number(b);
};

const lengthOptimization = (text, limit) => {
  if (!isString(text)) {
    return '';
  }

  if (text.length > limit) {
    return `${text.substring(0, limit)}...`;
  }
  return text;
};

const renderPrice = (item) => {
  const discountValue = item?.discount?.value;
  const fixPromotionValue = item?.fixPromotion?.value;
  try {
    if (item?.type === 'DISCOUNT_CARD' && discountValue) {
      if (Number(discountValue) > 0) {
        return {
          priceText: discountValue,
          priceUnit: '折',
        };
      }
      if (Number(discountValue) === 0) {
        return {
          priceText: '免费',
          priceUnit: null,
        };
      }
    } else if (item?.type === 'FIXED_PROMOITON_COUPON' && fixPromotionValue) {
      if (Number(fixPromotionValue) > 0) {
        return {
          priceText: `${money_US(fixPromotionValue) || '-'}`,
          priceUnit: '元',
        };
      }
      if (Number(fixPromotionValue) === 0) {
        return {
          priceText: '免费',
          priceUnit: null,
        };
      }
    }
    return {
      priceText: '-',
      priceUnit: null,
    };
  } catch (error) {
    log.addLog('renderPrice-error', 'success', { message: JSON.stringify(error?.message) });
    return {
      priceText: '-',
      priceUnit: null,
    };
  }
};

/**
 * 异步延迟
 * @param time - 延迟时间（毫秒）
 * @returns - 返回一个 Promise，当延迟结束后 resolve
 * 场景：多个弹框连续打开关闭时，弹框动画卡顿问题
 */
function animationDelay(time = 200) {
  return new Promise((resolve) => setTimeout(resolve, time));
}


const getHighestPriorityItem = (arr: any[]) => {
  const priority = {
    YEAR: 3,
    MONTH: 2,
    DAILY: 1,
  };
  // 用 maxBy 按 priority 找最大项
  const highestPriorityItem = maxBy(arr, (item) => priority[item.priceType] || 0);
  return highestPriorityItem?.priceType;
};

export {
  compareAmountToolMethod,
  lengthOptimization,
  renderPrice,
  animationDelay,
  getHighestPriorityItem,
};

