import { ajax, url } from '@ali/iec-dtao-utils';

// 获取url上的mock用户
const attachHackUserInfo = (urlInfo) => {
  const hackUserInfo = url.query('hackUserInfo');
  if (hackUserInfo) {
    return `${urlInfo}${/\?/.test(urlInfo) ? '&' : '?'}hackUserInfo=${hackUserInfo}`;
  }
  return urlInfo;
};

// get请求
export const get = <T>(params: { urlStr: string; data?: any }): Promise<T> => {
  const { urlStr, data } = params;
  const integratedUrl = attachHackUserInfo(urlStr);
  return ajax.get({ url: integratedUrl, data });
};

// post请求
export const post = <T>(params: { urlStr: string; data?: any }): Promise<T> => {
  const { urlStr, data } = params;
  const integratedUrl = attachHackUserInfo(urlStr);
  return ajax.post({ url: integratedUrl, data });
};
