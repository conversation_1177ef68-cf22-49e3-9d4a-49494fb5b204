import React from 'react';
import styles from './styles.module.scss';
import { Slider } from '@alifd/next';

const imgList = [
  'https://img.alicdn.com/imgextra/i4/O1CN01MWyFqa1lWMOiTVOMl_!!6000000004826-0-tps-480-1040.jpg',
  'https://img.alicdn.com/imgextra/i2/O1CN01hThlAI1I8tFGGTebX_!!6000000000849-0-tps-480-1040.jpg',
  'https://img.alicdn.com/imgextra/i2/O1CN016smmoT1dIbZo3N8df_!!6000000003713-0-tps-480-1040.jpg',
  'https://img.alicdn.com/imgextra/i3/O1CN01BTo0bb1LNx7ONKOPZ_!!6000000001288-0-tps-480-1040.jpg',
];

const SwipePanel = () => {
  return (
    <div className={styles.example}>
      <div className={styles['example-title']}>专属标识展示</div>
      <div className={styles['example-content']}>
        <Slider speed={2000} autoplay autoplaySpeed={5000}>
          {
            imgList.map((img) => {
              return <img src={img} />;
            })
          }
        </Slider>
      </div>
    </div>
  );
};

export default SwipePanel;
