.price-card {
  width: 1240px;
  margin: 0 auto;
  display: flex;
  gap: 24px;

  // 当只有一个项目时，居中显示
  &.single-item {
    justify-content: center;

    .item {
      width: 100%;
    }
  }

  // 当有两个项目时，并排显示
  &.two-items {
    justify-content: space-between;

    .item {
      width: calc((100% - 24px) / 2); // 两个卡片平分宽度，减去间距
    }
  }
}

.item {
  height: 153px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  border-radius: 12px;
  // background: linear-gradient(180deg, #fff7ed 0%, #fff0f0 100%);
  // border: 1px solid #e4e6ed;
  box-sizing: border-box;
}
.item-content {
  display: flex;
  flex-direction: column;
  height: 115px;
}

.item-title {
  display: flex;
  flex-direction: row;
  justify-content: center;
  margin-top: 12px;
}

.item-price {
  margin-top: 16px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: flex-end;

  .baseTrailAmount {
    display: flex;
    align-items: flex-end;
    gap: 3px;

    .amount {
      font-size: 24px;
      line-height: 30px;
      font-weight: 500;
      color: #111;
      margin-right: 3px;
    }

    .unit {
      font-size: 12px;
      color: #111;
    }
  }

  .base-trail-origin {
    display: flex;
    flex-direction: row;
    text-decoration: line-through;
    color: #666;
    font-size: 12px;
  }
}

.item-rule {
  display: flex;
  flex-direction: row;
  justify-content: center;
  margin-top: 8px;

  .item-rule-desc {
    font-size: 12px;
    color: #131313;
  }

  .item-rule-period {
    display: flex;
    flex-direction: row;
    font-size: 12px;
    color: #131313;
  }
}

.item-footer {
  display: flex;
  border-radius: 0 0 6px 6px;
  justify-content: center;
  align-items: center;
  margin-bottom: 0;
  padding: 9px 6px;
  height: 38px;
  flex-direction: row;
  // background: rgba(255, 210, 189, 0.35);
}
