/**
* @file index
* @date 2025-07-01
* <AUTHOR>
*/

import React from 'react';
import styles from './index.module.scss';
import PriceCardItem from '@/components/PriceCardItem';


interface PriceItem {
  priceType?: string;
  title?: string;
  subTitle?: string;
  desc?: string;
  isAdmit?: Boolean;
  promotionFlag?: Boolean;
  originalFeeAmount?: number | string;
  discountFeeAmount?: number | string;
  effectTime?: string | null;
  expireTime?: string | null;
  promotionDetailList?: any[];
  exemptionAmount?: number | string;
}

interface PriceCardProps {
  priceData: PriceItem[];
  onChange: (d) => string;
  selectedPriceType?: string;
}

const PriceCard: React.FC<PriceCardProps> = ({ priceData, onChange, selectedPriceType }) => {
  // 根据数据数组长度确定容器的CSS类名
  const getContainerClassName = () => {
    if (!priceData || priceData.length === 0) return styles['price-card'];
    if (priceData.length === 1) return `${styles['price-card']} ${styles['single-item']}`;
    if (priceData.length === 2) return `${styles['price-card']} ${styles['two-items']}`;
    return styles['price-card']; // 默认情况
  };

  const handleOnChangePrice = (item) => {
    onChange(item.priceType);
  };

  return (
    <div className={getContainerClassName()}>
      {priceData?.map((item, index) => (
        <div key={index} className={styles.item} onClick={() => handleOnChangePrice(item)}>
          <PriceCardItem
            item={item}
            selectedPriceType={selectedPriceType}
          />
        </div>
      ))}
    </div>
  );
};

export default PriceCard;
