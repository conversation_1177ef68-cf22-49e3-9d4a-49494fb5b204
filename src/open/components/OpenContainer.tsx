/**
* @file OpenContainer
* @date 2025-06-27
* <AUTHOR>
*/

import React from 'react';
import styles from './styles.module.scss';
import { PriceInfo } from '@/types';
import { Button } from '@alifd/next';
import PriceCard from './PriceCard';
import { NO_ADMIT_IMG } from '@/constant';

// const BASE_TRAIL_STYLE = { color: '#3D5EFF' };
const BUTTON_STYLE = {
  zhibo: {
    backgroundColor: '#FF3333',
    color: '#fff',
  },
};

interface OpenContainerProps {
  admitInfo: {
    isAdmit: boolean;
    rejectCode: string;
  };
  openPriceInfo: PriceInfo[];
  alipayLoginId: string;
  marketTitle: string;
  selectedPriceType?: string;
  onShowProtocol: () => void;
  onChangePriceType: (d) => any;
  onApply: (d) => any;
}

const renderUnAdmittedContent = ({ code, alipay }) => {
  switch (code) {
    case 'ARREARS':
      return <>您的店铺已欠费，暂时不支持开通退货宝服务，请尽快向服务费扣款支付宝账户{alipay || '-'}内充值</>;
    case 'OTHER':
      return <>建议您加强店铺经营，控制退货率</>;
    default:
      return <>建议您加强店铺经营，控制退货率</>;
  }
};

const OpenContainer = (props: OpenContainerProps) => {
  const {
    admitInfo,
    openPriceInfo,
    alipayLoginId,
    marketTitle,
    selectedPriceType,
    onShowProtocol,
    onChangePriceType,
    onApply,
  } = props;

  // 特殊渠道，主题色定制（eg直播平台）
  const applyButtonStyle = window?.location?.href?.includes('microapp') ? BUTTON_STYLE.zhibo : null;

  if (!admitInfo?.isAdmit) {
    return (
      <div className={styles['open-content']}>
        <img src={NO_ADMIT_IMG} className={styles['noAdmit-img']} />
        <div className={styles['noAdmit-title']}>暂不符合准入要求</div>
        <div className={styles['noAdmit-desc']}>{renderUnAdmittedContent({ code: admitInfo?.rejectCode, alipay: alipayLoginId })}</div>
      </div>
    );
  }

  return (
    <div className={styles['open-content']}>
      <div className={styles['open-content-fee']}>
        <PriceCard
          selectedPriceType={selectedPriceType}
          priceData={openPriceInfo}
          onChange={onChangePriceType}
        />
      </div>
      <div className={styles['open-content-protocol']}>
        请阅读<Button type="primary" text onClick={onShowProtocol}><span>《消费者体验提升计划协议》</span></Button>
      </div>
      <div className={styles['open-btn-wrap']}>
        {
          marketTitle && (
            <div className={styles['open-btn-wrap-tag']}>
              {marketTitle}
            </div>
          )
        }
        {
          applyButtonStyle ?
            <Button
              className={styles['open-content-btn']}
              style={{ backgroundColor: applyButtonStyle.backgroundColor, color: applyButtonStyle.color }}
              type="primary"
              onClick={onApply}
            >
              <span>同意协议并加入</span>
            </Button> :
            <Button
              className={styles['open-content-btn']}
              type="primary"
              onClick={onApply}
            >
              <span>同意协议并加入</span>
            </Button>
        }
      </div>
      <div className={styles['open-content-extend']}>
        <div className={styles['open-content-title']}>扣费说明</div>
        <div className={styles['open-content-desc']}>
          A. 本服务扣费方式：商品发货后，将从您店铺绑定支付宝账户 {alipayLoginId || '--'} 中自动扣除相应费用。<br />
          B. 加入本服务后，已订购的小件退货运费险将自动退出，新产生的订单运费险不再扣费。
        </div>
      </div>
    </div>
  );
};

export default OpenContainer;
