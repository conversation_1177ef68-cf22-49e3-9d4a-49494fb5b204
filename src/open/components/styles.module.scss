@import "../../font.scss";

.open-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  align-content: space-between;
  padding: 24px 0 42px 0;

  .noAdmit-img {
    width: 180px;
    height: 150px;
  }
  .noAdmit-title {
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    text-align: center;
    color: #111;
  }
  .noAdmit-desc {
    font-size: 12px;
    font-weight: normal;
    line-height: 18px;
    text-align: center;
    color: #999;
  }
  .open-content-fee {
    font-size: 14px;
    font-family: "AlibabaFontMd", sans-serif;
    width: 100%;
    .content-amount {
      display: inline-block;
      font-size: 30px;
      font-weight: 500;
    }
    .fee-half-promotion {
      display: flex;
      flex-direction: row;
      .fee-half-promotion-content {
        margin-top: 6px;
        font-size: 14px;
        font-weight: 500;
        color: #111;
        font-size: 14px;
        span {
          font-size: 30px;
        }
      }
      .fee-half-promotion-info {
        margin-top: 10px;
      }
    }
    .fixed-fee-panel {
      display: flex;
      gap: 12px;
    }
  }

  .open-content-alipayInfo {
    display: flex;
    flex-direction: row;
    justify-content: center;
    margin-top: 6px;
    min-width: 545px;
    height: 30px;
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 12px;
    font-weight: normal;
    line-height: 18px;
    color: #666;
    span {
      color: #111;
    }
  }
  .open-content-protocol {
    width: 320px;
    height: 18px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    margin-top: 24px;
    padding: 0;
    font-size: 12px;
    gap: 6px;
    color: #999;
    span {
      font-size: 12px;
    }
  }
  .open-btn-wrap {
    position: relative;
    display: inline-block;
    margin-bottom: 24px;
    .open-btn-wrap-tag {
      position: absolute;
      color: #ff5000;
      padding: 3px 9px;
      line-height: 18px;
      top: -8px;
      right: 0;
      z-index: 1;
      width: auto;
      height: 24px;
      border-radius: 12px;
      background-color: #ffede5;
    }
    .open-content-btn {
      margin-top: 6px;
      width: 320px;
      height: 42px;
      border-radius: 4px;
    }
  }
  .open-content-extend {
    padding: 6px 12px;
    color: #666;
    width: 100%;
    background: #f7f8fa;
    border-radius: 6px;
    .open-content-title {
      font-size: 14px;
      line-height: 20px;
      margin-bottom: 6px;
    }
    .open-content-desc {
      font-size: 12px;
      line-height: 18px;
    }
  }
}

.example {
  width: 240px;
  height: 500px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  .example-title {
    margin: 0 auto 12px auto;
    font-size: 14px;
    font-weight: 600;
    line-height: 20px;
    letter-spacing: 0;
    color: #111;
  }
  .example-content {
    display: flex;
    flex-direction: column;
    width: 248px;
    height: 548px;
    img {
      border-radius: 20px;
      width: 240px;
      height: 494px;
      background-repeat: no-repeat;
    }
  }
}
