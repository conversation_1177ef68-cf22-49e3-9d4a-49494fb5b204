import React, { useEffect, useState, useCallback, useRef, useMemo } from 'react';
import MenuHeader from '@/components/MenuHeader';
import { ENoticeType, Notices } from '@ali/mkt-design';
import styles from './index.module.scss';
import { BaseOpenProps, PriceInfo } from 'src/types';
import { queryAlipayInfo, queryMerchantArrears, queryMerchantStatus } from '@/api/query';
import { baseAdmitAndTrailInfo, baseAndFixedOpen, baseOpen, batchAdmitFixedPrice } from '@/api/open';
import { getCookie } from '@/utils/cookie';
import { Message, Dialog, Loading, Drawer } from '@alifd/next';
import { baxia, log } from '@ali/iec-dtao-utils';
import { ACCOUNT_ARREARS_TEXT, BAXIA_REGEX, ByfOpenBtnMarketingTextPC, HIGH_RISK_TEXT, MARKET_TIPS_CONTENT, OFFLINE_PAYMENT_URL, OPEN_FAILED_CODE, PROTOCOL_URL, REDOUND_ARROW, TRADE_ACCOUNT, PRICE_RENDER_CONFIG, QIANNIU_PC } from '../constant';
import { getUrlParams } from '@/utils/params';
import CommonQa from '@/components/CommonQa';
import TipsBar from '@/components/TipsBar';
import { queryDelivery } from '@/api/promotion';
import { get } from 'lodash-es';
import OpenContainer from './components/OpenContainer';
import { getHighestPriorityItem } from '@/utils/tools';

let authTimeId;

const ACCOUNT_ARREARS_TIPS = {
  type: ENoticeType.warn,
  content: ACCOUNT_ARREARS_TEXT,
  url: OFFLINE_PAYMENT_URL,
  buttonText:
  <div
    className={styles['redound-arrow']}
    onClick={() => log.addLog('open_Repay_btn', 'click')}
  >去还款 <img src={REDOUND_ARROW} />
  </div>,
};

const HIGH_RISK_TIPS = {
  type: ENoticeType.warn,
  content: HIGH_RISK_TEXT,
};

const Open = (props) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isShowProtocol, setIsShowProtocol] = useState<boolean>(false);
  const [traceLog, setTraceLog] = useState<string | null>();
  const [arrearsInfo, setArrearsInfo] = useState<{ isArrears: boolean; showArrears: boolean }>({ isArrears: false, showArrears: false });
  const [alipayLoginId, setAlipayLoginId] = useState<string>('');
  const isClickApplyBtn = useRef(false);
  const [selectedPriceType, setSelectedPriceType] = useState('');
  const [isHighRisk, setIsHighRisk] = useState<boolean>(false);
  const [status, setStatus] = useState<string>('null');

  // 准入信息
  const [admitInfo, setAdmitInfo] = useState({
    isAdmit: true,
    rejectCode: '',
  });

  // 价格信息
  const [openPriceInfo, setOpenPriceInfo] = useState<PriceInfo[]>([]);

  const [marketTitle, setMarketTitle] = useState<string>('');

  const baxiaInit = useCallback(() => {
    const callback = (url: string) => {
      if (BAXIA_REGEX.test(url)) {
        return true;
      }
      return false;
    };
    baxia.setBaxiaInitSafe(callback);
  }, []);

  // 欠费和高退合并一个公告栏
  const tips = useMemo(() => {
    const tipsArray: any = [];
    if (arrearsInfo.isArrears && arrearsInfo.showArrears) {
      tipsArray.push(ACCOUNT_ARREARS_TIPS);
    }
    if (isHighRisk) {
      tipsArray.push(HIGH_RISK_TIPS);
    }
    return tipsArray;
  }, [isHighRisk, arrearsInfo]);


  const handleOnGetArrearsAmount = async () => {
    try {
      const arrearsData = await queryMerchantArrears();
      const { responseCode, arrears, showArrears } = arrearsData;
      if (responseCode !== 'SUCCESS') {
        log.addLog('query-arrears-amount', 'error', { responseCode });
      }
      setArrearsInfo({ showArrears, isArrears: arrears });
    } catch (error) {
      log.addLog('query-arrears-amount', 'error', { catch: error?.message });
    }
  };

  const initLog = () => {
    try {
      log.reportInit();
      baxiaInit();
      const traceLogData = getUrlParams(window.location.href, 'traceLog');
      traceLogData && setTraceLog(traceLogData);
      log.addLog('byf-open', 'visit', { from: traceLogData ?? null });
    } catch (error) {
      log.addLog('byf-open', 'error', { catch: error?.message });
    }
  };

  const handleOnGetMarketInfo = async () => {
    try {
      const marketRes = await queryDelivery({ positionCode: ByfOpenBtnMarketingTextPC });
      const title = get(marketRes, 'content.items.0.fixedContent') ?? null;
      return title;
    } catch (error) {
      return null;
    }
  };

  // 获取日常准入试算+框类准入试算
  const handleOnGenerateAdmitAndTrail = () => {
    setIsLoading(true);
    Promise.allSettled([
      baseAdmitAndTrailInfo(),
      batchAdmitFixedPrice(),
    ]).then((res: any) => {
      setIsLoading(false);
      const [baseRes, frameRes] = res;
      const { status: baseStatus, value: baseValue } = baseRes || {};
      const { status: frameStatus, value: frameValue } = frameRes || {};
      if (baseStatus !== 'fulfilled') {
        log.addLog('base-admit-and-trail-query', 'error', { error: JSON.stringify(baseRes?.reason) });
        setAdmitInfo({
          isAdmit: false,
          rejectCode: 'ERROR',
        });
        return;
      }

      // 日常数据
      const baseInfoData = {
        priceType: 'DAILY',
        title: PRICE_RENDER_CONFIG.DAILY?.title,
        subTitle: PRICE_RENDER_CONFIG.DAILY?.subTitle,
        desc: PRICE_RENDER_CONFIG.DAILY?.desc,
        isAdmit: baseValue?.isAdmit,
        promotionFlag: baseValue?.promotionFlag,
        originalFeeAmount: baseValue?.originalFeeAmount,
        discountFeeAmount: baseValue?.discountFeeAmount,
        promotionDetailList: baseValue?.promotionDetailList,
        exemptionAmount: baseValue?.exemptionAmount,
      };

      setIsHighRisk(baseValue?.hasHighRiskRefund);

      // 不准入（包含日常接口异常）
      if (baseValue?.responseCode !== 'SUCCESS' || !baseValue?.isAdmit) {
        !baseValue?.isAdmit && log.addLog('base-admit-reject', 'error', { admit: baseValue?.isAdmit, rejectCode: baseValue?.customerRejectCode });
        setAdmitInfo({
          isAdmit: false,
          rejectCode: baseValue?.customerRejectCode || 'ERROR',
        });
        return;
      }

      // 框类数据
      const {
        responseCode: fixedResponseCode,
        fixedAdmitAndTrailInfoList,
      } = frameValue;

      if (frameStatus !== 'fulfilled' || fixedResponseCode !== 'SUCCESS') {
        log.addLog('fixed-admit-and-trail-query', 'error');
      }

      const fixedInfoData: any[] = [];
      fixedAdmitAndTrailInfoList?.forEach((item) => {
        if (item?.isAdmit) {
          log.addLog(`fixed-admit-access-${item?.fixedPriceType}`, 'success');
          fixedInfoData.push({
            priceType: item?.fixedPriceType,
            title: PRICE_RENDER_CONFIG[item?.fixedPriceType]?.title,
            subTitle: PRICE_RENDER_CONFIG[item?.fixedPriceType]?.subTitle,
            desc: PRICE_RENDER_CONFIG[item?.fixedPriceType]?.desc,
            isAdmit: item?.isAdmit,
            promotionFlag: item?.promotionFlag,
            originalFeeAmount: item?.originalFeeAmount,
            discountFeeAmount: item?.discountFeeAmount,
            exemptionAmount: item?.exemptionAmount,
            effectTime: item?.effectTime,
            expireTime: item?.expireTime,
            promotionDetailList: item?.promotionDetailList,
          });
        }
      });

      fixedInfoData?.length === 0 && log.addLog('fixed-admit-reject', 'success');

      const openInfoList = [...fixedInfoData, baseInfoData];
      setOpenPriceInfo(openInfoList);
      setSelectedPriceType(getHighestPriorityItem(openInfoList));
    }).catch(() => {
      log.addLog('base-fixed-query', 'error');

      setIsLoading(false);

      setAdmitInfo({
        isAdmit: false,
        rejectCode: 'ERROR',
      });
    });
  };

  // 获取用户状态
  const handleOnGetStatus = async (checkStatus) => {
    try {
      const statusRes = await queryMerchantStatus();
      const { status: statusData, responseCode: statusResponseCode, failedCode: statusFailedCode } = statusRes;
      if (statusResponseCode !== 'SUCCESS') {
        return;
      }
      setStatus(statusData);
      switch (statusData) {
        case 'null':
        case 'MERCHANT_QUIT_SUCCEED':
          Message.hide();
          break;
        case 'MERCHANT_OPENING':
          Message.show({
            type: 'loading',
            title: '开通中',
            content: '请耐心等待',
            duration: 0,
            hasMask: true,
            align: 'tc tc',
            offset: [0, 200],
          });
          checkStatus();
          return;
        case 'MERCHANT_OPEN_FAILED':
          log.addLog('open-fail-access', 'error', { from: traceLog ?? null, code: statusFailedCode ?? 'DEFAULT_FRONT_ERROR', priceType: selectedPriceType });
          // 仅失败命中点击过开通，则提示失败文案
          if (isClickApplyBtn.current === true) {
            Message.show({
              type: 'error',
              title: statusFailedCode ? OPEN_FAILED_CODE[statusFailedCode] || OPEN_FAILED_CODE?.DEFAULT_FRONT_ERROR : OPEN_FAILED_CODE?.DEFAULT_FRONT_ERROR,
              align: 'tc tc',
              hasMask: true,
              offset: [0, 200],
              duration: 2500,
            });
            log.addLog(`open-fail-${statusFailedCode}`, 'success', { from: traceLog ?? null });
          }
          break;
        case 'MERCHANT_OPEN_SUCCEED':
        case 'MERCHANT_FROZEN':
        case 'MERCHANT_QUITTING':
          Message.success('开通成功');
          log.addLog('open-success', 'success', { from: traceLog ?? null, priceType: selectedPriceType });
          props?.history.push('/manage');
          return;
      }
      // 未开获取准入试算信息
      handleOnGenerateAdmitAndTrail();
      // 获取营销文案
      const marketTitleRes = await handleOnGetMarketInfo();
      setMarketTitle(marketTitleRes);
    } catch (error) {
      log.addLog('base-admit-and-trail', 'error', { catch: error?.message });
    }
  };

  // 日常开通申请
  const handleOnApply = async (checkInfo?: { isChecked?: boolean }) => {
    setIsLoading(true);
    const priceInfo = openPriceInfo?.filter((item) => item?.priceType === selectedPriceType)[0];
    if (!priceInfo) {
      setIsLoading(false);
      Dialog.confirm({
        v2: true,
        title: '开通失败，请稍后再试',
        footerActions: ['ok'],
        messageProps: {
          type: 'error',
        },
      });
      return;
    }
    log.addLog('open-click-daily', 'click', { from: traceLog });
    try {
      const params: BaseOpenProps = {
        identifier: `${getCookie()}-${Math.floor(Date.now())}`,
        showFeeAmount: priceInfo?.originalFeeAmount,
        isChecked: checkInfo?.isChecked,
      };
      priceInfo?.promotionFlag && (params.showDiscountFeeAmount = priceInfo?.discountFeeAmount);
      traceLog === 'qnByfMarket' && (params.openChannel = 'qianniu_migration_to_byf');
      const applyData = await baseOpen(params);
      const { responseCode, applicationNo } = applyData;
      setIsLoading(false);
      // 其他失败
      if (responseCode !== 'SUCCESS' || !applicationNo) {
        log.addLog('open-daliy-fail', 'error', { responseCode });
        Dialog.confirm({
          v2: true,
          title: '开通失败，请稍后再试',
          footerActions: ['ok'],
          messageProps: {
            type: 'error',
          },
        });
        return;
      }
      log.addLog('open-daliy-success', 'success', { from: traceLog });
      checkUserStatus();
    } catch (error) {
      setIsLoading(false);
      log.addLog('open-fail', 'error', { catch: error?.message });
      Dialog.confirm({
        v2: true,
        title: '开通失败，请稍后再试',
        footerActions: ['ok'],
        messageProps: {
          type: 'error',
        },
      });
    }
  };

  // 获取支付宝信息
  const handleOnGetAlipayInfo = async () => {
    try {
      const alipayInfoData = await queryAlipayInfo({ channel: TRADE_ACCOUNT });
      const { responseCode: alipayInfoResponseCode, alipayLoginId: alipayLoginIdData } = alipayInfoData;
      if (alipayInfoResponseCode !== 'SUCCESS') {
        log.addLog('query-alipay-info', 'error', { responseCode: alipayInfoResponseCode });
        return;
      }
      setAlipayLoginId(alipayLoginIdData);
    } catch (error) {
      log.addLog('query-alipay-info', 'error', { catch: error?.message });
    }
  };

  // 直接开通一口价
  const handleOnApplyFixed = async () => {
    setIsLoading(true);
    try {
      const fixedInfo = openPriceInfo?.filter((item) => item?.priceType === selectedPriceType)[0];
      if (!fixedInfo) {
        setIsLoading(false);
        Dialog.confirm({
          v2: true,
          title: '开通失败，请稍后再试',
          footerActions: ['ok'],
          messageProps: {
            type: 'error',
          },
        });
        return;
      }
      log.addLog('open-click-fixed', 'click', { from: traceLog, priceType: fixedInfo?.priceType });
      const params: any = {
        identifier: `${getCookie()}-${Math.floor(Date.now())}`,
        fixedPriceEffectTime: fixedInfo?.effectTime,
        fixedPriceExpireTime: fixedInfo?.expireTime,
        fixedPriceType: fixedInfo?.priceType,
        showFeeAmount: fixedInfo?.originalFeeAmount,
        showDiscountFeeAmount: fixedInfo?.discountFeeAmount,
        openChannel: QIANNIU_PC,
        umdiToken: '',
      };
      const fixedRes = await baseAndFixedOpen(params);
      const { responseCode } = fixedRes;
      if (responseCode !== 'SUCCESS') {
        setIsLoading(false);
        log.addLog('open-fixed-fail', 'error', { responseCode, from: traceLog, priceType: fixedInfo?.priceType });
        return;
      }
      log.addLog('open-fixed-success', 'success', { from: traceLog, priceType: fixedInfo?.priceType });
      checkUserStatus();
    } catch (error) {
      setIsLoading(false);
    }
  };

  // 同意协议并开通
  const onApply = () => {
    setIsLoading(true);
    isClickApplyBtn.current = true;
    log.addLog('open-click', 'click', { from: traceLog ?? null });
    if (selectedPriceType === 'DAILY') {
      handleOnApply();
    } else {
      handleOnApplyFixed();
    }
  };

  // 切换价格类型
  const handleOnChangePriceType = (data) => {
    setSelectedPriceType(data);
  };

  const checkUserStatus = () => {
    const checkStatus = () => {
      authTimeId = setTimeout(() => handleOnGetStatus(checkStatus), 2000);
    };
    handleOnGetStatus(checkStatus);
  };

  useEffect(() => {
    checkUserStatus();
    return () => clearTimeout(authTimeId);
  }, []);


  useEffect(() => {
    initLog();
    handleOnGetArrearsAmount();
    handleOnGetAlipayInfo();
  }, []);

  return (
    <div className={styles['open-base-panel']}>
      <Loading visible={isLoading}>
        {tips.length > 0 && <Notices dataSource={tips} showPagination />}
        <MenuHeader status={status} />
        {openPriceInfo[0]?.isAdmit && <TipsBar content={MARKET_TIPS_CONTENT} />}
        <OpenContainer
          admitInfo={admitInfo}
          openPriceInfo={openPriceInfo}
          marketTitle={marketTitle}
          alipayLoginId={alipayLoginId}
          selectedPriceType={selectedPriceType}
          onShowProtocol={() => { setIsShowProtocol(true); }}
          onChangePriceType={handleOnChangePriceType}
          onApply={onApply}
        />
        <div className={styles['open-qa']}>
          <CommonQa noTitle={false} isLogoOverflow={false} />
        </div>
        <Drawer
          title="协议签署"
          placement="right"
          visible={isShowProtocol}
          width={600}
          onClose={() => { setIsShowProtocol(false); }}
        >
          <div style={{ padding: '0 22px' }}>
            <iframe
              width={550}
              height={710}
              style={{ border: 'none' }}
              src={PROTOCOL_URL}
            />
          </div>
        </Drawer>
      </Loading>
    </div>
  );
};
export default Open;
