.details {
  display: flex;
  flex-direction: column;
  margin: 0 auto;
  width: 1220px;
  padding: 24px;
  .details-head {
    display: flex;
    flex-direction: row;
    margin-bottom: 12px;
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    letter-spacing: 0;
    color: #111;
  }
  .details-info {
    display: flex;
    flex-direction: row;
    margin: 0 0 17px 0;
    line-height: 26px;
    align-items: center;
    font-size: 14px;
    font-weight: normal;
    color: #666;
    .details-info-icon {
      width: 12px;
      height: 12px;
      margin-right: 2px;
      background-image: url("https://img.alicdn.com/imgextra/i4/O1CN01WOM9TZ1b5f8zdsAry_!!6000000003414-2-tps-24-24.png");
      background-size: 12px 12px;
      background-repeat: no-repeat;
    }
  }
  .details-content {
    display: flex;
    flex-direction: row;
    .details-content-main {
      display: flex;
      flex-direction: column;
      width: 790px;
    }
    .details-content-right {
      display: flex;
      flex-direction: column;
      width: 348px;
      margin-left: 24px;
      padding: 0 24px;
      border-width: 0 0 0 1px;
      border-style: solid;
      border-color: #f0f2fa;
    }
  }
  .details-footer {
    position: fixed;
    bottom: 0;
    height: 60px;
    margin-left: 550px;
  }
}

.service-title {
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  letter-spacing: 0;
  color: #111;
}

.config-number {
  margin-left: 2px;
  font-family: "AlibabaFontMd";
  font-size: 14px;
  font-weight: 500;
  line-height: 100%;
  letter-spacing: 0;
  color: #111;
}
.config-gray-number-no-line {
  font-size: 12px;
  font-weight: normal;
  line-height: 100%;
  letter-spacing: 0;
  color: #999;
  padding-left: 6px;
}
.config-gray-number-balloon-icon {
  color: #999;
  margin-top: -4px;
  margin-left: 3px;
  cursor: pointer;
  font-size: 12px;
}
.config-gray-number {
  font-size: 14px;
  font-weight: normal;
  line-height: 100%;
  letter-spacing: 0;
  text-decoration: line-through;
  text-decoration-color: rgba(153, 153, 153, 0.5);
  font-family: "AlibabaFontMd";
  color: #999;
}
.service-copy {
  display: flex;
  flex-direction: row;
  .service-copy-text {
    margin-right: 4px;
  }
}

.promotionPopup {
  display: flex;
  flex-direction: row;
  width: 76px;
  height: 18px;
  background-color: rgba(255, 128, 0, 0.06);
  padding: 3px 6px;
  border-radius: 6px;
  align-items: center;
  gap: 3px;

  .promotionIcon {
    width: 12px;
    height: 12px;
    background-size: 12px 12px;
    background-image: url("https://img.alicdn.com/imgextra/i4/O1CN01mHalAj1sPfylb4bVS_!!6000000005759-2-tps-24-24.png");
  }
  
  .promotionTitle {
    font-size: 12px;
    color: #ff8000;
    line-height: 18px;
    font-weight: 400;
  }
}
