import React from 'react';
import styles from './index.module.scss';
import dayjs from 'dayjs';
import { Grid } from '@alifd/next';
import { money_US } from '@ali/iec-dtao-utils/esm/common/number';
import { CLAIM_STATUS_INFO, NO_CLAIMS_DATA_IMG, PAYEE_TYPE } from '@/constant';

const { Row, Col } = Grid;

const ClaimsList = ({ data }) => {
  if (!data || data?.length === 0) {
    return (
      <div className={styles['no-claims-data']}>
        <img src={NO_CLAIMS_DATA_IMG} />
        <div className={styles['no-claims-data-desc']}>暂无数据</div>
      </div>
    );
  }
  return (
    <div className={styles['claims-list']}>
      {
        data.map((item) => {
          const tag = {
            bgColor: CLAIM_STATUS_INFO[item?.compensationStatus]?.bgColor ?? '',
            color: CLAIM_STATUS_INFO[item?.compensationStatus]?.color ?? '',
          };
          return (
            <div className={styles['claims-list-item']}>
              <Row className={styles['claims-list-item-row']}>
                <Col span="4" className={styles['claims-list-item-label']} >
                  服务状态
                </Col>
                <Col span="20" className={styles['claims-list-item-value']} >
                  <div
                    className={styles['claims-list-item-value-tag']}
                    style={{ backgroundColor: tag?.bgColor, color: tag?.color }}
                  >
                    {item.title}
                  </div>
                  {item?.isRedEnvelopeClaim && <span>已补偿至买家的淘宝红包账户</span>}
                </Col>
              </Row>
              <Row className={styles['claims-list-item-row']}>
                <Col span="4" className={styles['claims-list-item-label']} >
                  申请时间
                </Col>
                <Col span="16" className={styles['claims-list-item-value']} >
                  {item?.applyTime ? dayjs(item?.applyTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
                </Col>
              </Row>
              <Row className={styles['claims-list-item-row']}>
                <Col span="4" className={styles['claims-list-item-label']} >
                  完结时间
                </Col>
                <Col span="16" className={styles['claims-list-item-value']} >
                  {item?.compensatedTime ? dayjs(item?.compensatedTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
                </Col>
              </Row>
              {
                item?.compensatedAmount && (
                  <Row className={styles['claims-list-item-row']}>
                    <Col span="4" className={styles['claims-list-item-label']} >
                      补偿金额
                    </Col>
                    <Col span="16" className={styles['claims-list-item-value']} >
                      ¥{money_US(item.compensatedAmount) || '-'}
                    </Col>
                  </Row>
                )
              }
              {
                item?.payee === 'BUYER' && item?.compensationStatus === 'SUCCEEDED' && (
                  <Row className={styles['claims-list-item-row']}>
                    <Col span="4" className={styles['claims-list-item-label']} >
                      支付对象
                    </Col>
                    <Col span="16" className={styles['claims-list-item-value']} >
                      {PAYEE_TYPE[item.payee] ?? '-'}
                    </Col>
                  </Row>
                )
              }
            </div>
          );
        })
      }
    </div>
  );
};

export default ClaimsList;
