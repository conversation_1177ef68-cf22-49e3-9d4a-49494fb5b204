import React from 'react';
import styles from './index.module.scss';
import dayjs from 'dayjs';
import { Balloon } from '@alifd/next';

const ShopItemList = ({ data, order }) => {
  if (!data) {
    return <>暂无数据</>;
  }
  return (
    <div className={styles['shop-item-list']}>
      <div className={styles['shop-item-list-total']}>共计{data?.length}个商品</div>
      <div className={styles['shop-item-list-content']}>
        {
          data.map((item) => {
            return (
              <div className={styles['content-item']}>
                <img className={styles['content-item-img']} src={item.itemPicUrl} />
                <div className={styles['content-item-desc']}>
                  <div className={styles['item-desc-title']}>
                    {item.itemName}
                  </div>
                  {
                    item?.skuName?.length < 15 &&
                    (
                      <div className={styles['item-desc-info']}>
                        {item?.skuName}
                      </div>
                    )
                  }
                  {
                    item?.skuName?.length >= 15 &&
                    (
                      <>
                        <Balloon
                          v2
                          trigger={
                            <div className={styles['item-desc-info']}>
                              {`${item.skuName?.slice(0, 10)}...`}
                            </div>
                          }
                          align="b"
                          triggerType="hover"
                          style={{ width: 300 }}
                          closable={false}
                        >
                          {item.skuName}
                        </Balloon>
                      </>
                    )
                  }
                  <div className={styles['item-desc-info']}>
                    订单号 {item?.relatedSubOrderId || '-'}
                  </div>
                  <div className={styles['item-desc-info']}>
                    支付时间 {dayjs(order?.orderPayTime).format('YYYY-MM-DD HH:mm:ss')}
                  </div>
                </div>
              </div>
            );
          })
        }
      </div>
    </div>
  );
};

export default ShopItemList;
