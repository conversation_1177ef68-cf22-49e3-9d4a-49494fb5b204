import React from 'react';
import styles from './index.module.scss';
import { Grid } from '@alifd/next';

const { Row, Col } = Grid;

const DetailsCard = ({ config, isBorder = false }) => {
  if (!config) {
    return <>暂无信息</>;
  }
  return (
    <div className={styles['details-card']} style={{ border: isBorder ? '1px solid #E4E6ED' : 'none', width: 700 }}>
      {
        config.map((item) => {
          return (
            <Row style={{ lineHeight: '18px', fontSize: 12, marginBottom: 3 }}>
              <Col span="4" style={{ lineHeight: '18px', color: '#999999' }}>{item.label}</Col>
              <Col span="20" style={{ lineHeight: '18px', color: '#111' }}>{item.value}</Col>
            </Row>
          );
        })
      }
    </div>
  );
};

export default DetailsCard;
