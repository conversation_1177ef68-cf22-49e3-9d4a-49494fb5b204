import React, { useEffect, useState } from 'react';
import styles from './index.module.scss';
import CommonTag from '@/components/CommonTag';
import { queryClaimOrderList, queryServiceOrderInfo } from '@/api/query';
import { Button, Icon, Message } from '@alifd/next';
import { ClaimDetailInfoListItem, OrderInfoProps, ServiceOrderItem } from '@/types';
import dayjs from 'dayjs';
import { money_US } from '@ali/iec-dtao-utils/esm/common/number';
import DetailsCard from './components/DetailsCard';
import ShopItemList from './components/ShopItemList';
import ClaimsList from './components/ClaimsList';
import { getUrlParams } from '@/utils/params';
import { log } from '@ali/iec-dtao-utils';
import { PAYEE_TYPE_STATUS, SERVICE_STATUS_INFO, COMPENSATION_RED_ENVELOPE } from '@/constant';
import { set, minBy, includes } from 'lodash-es';
import { compareAmountToolMethod } from '@/utils/tools';
import DetailPromotionPopup from '@/components/DetailPromotionPopup';

// 补贴状态集合
const SCENE_LIST = ['OFFICIAL_PICK_UP', 'CAINIAO_PICK_UP', 'DELIVERY_SELF', 'MANUAL', 'SELF_HELP_APPEAL', 'ANT_SELF_HELP_APPEAL', 'INTERCEPT_LOGISTIC'];
// 物流拦截状态集合
const LOGISTICS_INTERCEPT_STATUS = ['SYSTEM_INTERCEPT_SEND_BACK', 'SYSTEM_INTERCEPT_CHANGE'];
interface ServiceItem {
  label: string;
  value: React.JSX.Element;
}
// 定义优先级顺序
const priority = {
  PROMOTION_SUBSIDY: 1,
  BIZ_SUBSIDY: 2,
  PLATFORM_SUBSIDY: 3,
};

const COUPON_NAME = {
  PROMOTION_SUBSIDY: '活动优惠',
  BIZ_SUBSIDY: '行业优惠',
  PLATFORM_SUBSIDY: '平台优惠',
};

const Details = (props) => {
  const [tag, setTag] = useState<{
    bgColor: string;
    color: string;
    title: string;
  }>();
  const [serviceDetails, setServiceDetails] = useState<{
    serviceStatus: string;
    orderInfo: OrderInfoProps;
    serviceInfo: ServiceItem[];
    serviceOrderItemList: ServiceOrderItem[];
  }>();
  const [claimsList, setClaimsList] = useState<ClaimDetailInfoListItem[]>();
  const mainOrderId = getUrlParams(window.location.href, 'mainOrderId');

  const handleOnCopy = (text) => {
    const input = document.createElement('textarea');
    document.body.appendChild(input);
    input.value = text;
    input.select();
    const action = document.queryCommandEnabled('copy') ? 'copy' : '';
    if (!action) return false;

    try {
      document.execCommand(action);
      Message.success('复制成功');
      return true;
    } catch (e) {
      return false;
    } finally {
      document.body.removeChild(input);
    }
  };

  const handleOnGetServiceInfo = async () => {
    const params = {
      mainOrderId,
    };
    const serviceData = await queryServiceOrderInfo(params);
    const { responseCode,
      serviceStatus,
      serviceInfo,
      orderInfo,
      serviceOrderItemList } = serviceData;
    if (responseCode !== 'SUCCESS') {
      log.addLog('query-order-info', 'error', { responseCode });
      Message.error('请求失败，请稍后再试');
      return;
    }
    const {
      serviceNo,
      effectiveTime,
      expireTime,
      actualFeeAmount,
      originalFeeAmount,
      selfPostageGuaranteedAmount,
      promotionDetails,
    } = serviceInfo;

    const highestPriorityPromotion: any = minBy(promotionDetails, (item) => priority[item?.subsidyType]);
    const priorityName = COUPON_NAME[highestPriorityPromotion?.subsidyType];

    const serviceInfoConfig = [{
      label: '服务单号',
      value:
  <div className={styles['service-copy']}>
    <div id="serviceNo" className={styles['service-copy-text']}>{serviceNo ?? '-'}</div>
    <div onClick={() => handleOnCopy(serviceNo)}><Icon type="copy" /></div>
  </div>,
    }, {
      label: '保障期限',
      value: <> {effectiveTime ? dayjs(effectiveTime).format('YYYY-MM-DD') : '-'} ~ {expireTime ? dayjs(expireTime).format('YYYY-MM-DD') : '-'}</>,
    }, {
      label: '服务费',
      value:
  <div style={{ display: 'flex', flexDirection: 'column' }}>
    <div style={{ display: 'flex', flexDirection: 'row' }}>
      {
              priorityName && (
                <div className={styles.promotionPopup}>
                  <div className={styles.promotionIcon} />
                  <div className={styles.promotionTitle}>{priorityName}</div>
                </div>
              )
            }
      <span className={styles['config-number']}>¥{money_US(actualFeeAmount ?? '-')}</span>
      {compareAmountToolMethod('fee-amount-compare', actualFeeAmount, originalFeeAmount) && (
      <span className={styles['config-gray-number-no-line']}>
        优惠前价：
        <span className={styles['config-gray-number']}>
          ¥{money_US(originalFeeAmount ?? '-')}
        </span>
      </span>
      )}
    </div>
    <div style={{ display: 'flex', flexDirection: 'row' }}>
      {
              promotionDetails?.length > 0 && (
                <div style={{ marginLeft: 6 }}>
                  <DetailPromotionPopup
                    promotionDetailList={promotionDetails}
                  />
                </div>
              )
            }
    </div>
  </div>,
    }, {
      label: '保障额度',
      value: <>自行寄回，预计补偿<span className={styles['config-number']}>¥{money_US(selfPostageGuaranteedAmount ?? '-')}</span> ; 上门取件，保首重，自动减免</>,
    }];
    setTag(SERVICE_STATUS_INFO[serviceStatus ?? 'null']);
    setServiceDetails({
      serviceStatus,
      serviceInfo: serviceInfoConfig,
      serviceOrderItemList,
      orderInfo,
    });
  };

  const handleOnGetClaimsList = async () => {
    const params = {
      mainOrderId,
      sceneList: SCENE_LIST,
    };
    try {
      const claimsData = await queryClaimOrderList(params);
      const { responseCode, claimDetailInfoList } = claimsData;
      if (responseCode !== 'SUCCESS') {
        log.addLog('query-claims-list', 'error', { responseCode });
        Message.error('请求失败，请稍后再试');
      }
      const claimData = claimDetailInfoList?.map((item) => {
        const itemData = {
          compensationStatus: item?.compensationStatus,
          applyTime: item?.applyTime,
          compensatedTime: item?.compensatedTime,
          isRedEnvelopeClaim: item?.specialDescCode === COMPENSATION_RED_ENVELOPE && item?.payee === 'BUYER' && item?.compensationStatus === 'SUCCEEDED', // 是否红包赔付
        };
        // 存在物流拦截
        if (includes(LOGISTICS_INTERCEPT_STATUS, item?.compensationSource)) {
          set(itemData, 'title', '快递拦截成功，拦截费用由退货宝与快递公司结算');
          return itemData;
        }
        switch (item?.compensationStatus) {
          case 'PAY_WAITING':
            set(itemData, 'title', '审核中');
            break;
          case 'FAILED':
            set(itemData, 'title', '审核不通过');
            break;
          case 'SUCCEEDED':
            item?.payee === 'BUYER' && set(itemData, 'compensatedAmount', item?.compensatedAmount);
            set(itemData, 'payee', item?.payee);
            set(itemData, 'title', item?.payee ? PAYEE_TYPE_STATUS[item?.payee] : '-');
            break;
          default:
            break;
        }
        return itemData;
      });
      setClaimsList(claimData);
    } catch (error) {
      log.addLog('query-claims-list', 'error', { catch: error?.message });
      Message.error('请求失败，请稍后再试');
    }
  };

  useEffect(() => {
    log.addLog('byf-detail', 'visit');
    handleOnGetServiceInfo();
    handleOnGetClaimsList();
  }, [mainOrderId]);

  return (
    <div className={styles.details}>
      <div className={styles['details-head']}>
        消费者体验提升计划
        <CommonTag bgcolor={tag?.bgColor} color={tag?.color} text={tag?.title} />
      </div>
      <div className={styles['details-content']}>
        <div className={styles['details-content-main']}>
          <div className={styles['details-content-main-service']}>
            <div className={styles['service-title']}>服务信息</div>
            <div className={styles['service-content']}>
              <DetailsCard config={serviceDetails?.serviceInfo} />
            </div>
          </div>
          <div className={styles['details-content-main-claims']}>
            <div className={styles['service-title']}>服务进度</div>
            <div className={styles['service-content']}>
              <ClaimsList data={claimsList} />
            </div>
          </div>
        </div>
        <div className={styles['details-content-right']}>
          <div className={styles['service-title']}>商品信息</div>
          <div className={styles['service-content']}>
            <ShopItemList
              order={serviceDetails?.orderInfo}
              data={serviceDetails?.serviceOrderItemList}
            />
          </div>
        </div>
      </div>
      <div className={styles['details-footer']}>
        <Button
          type="primary"
          onClick={() => {
            props?.history.push('/manage');
          }}
        >进入体验提升计划首页
        </Button>
      </div>
    </div>
  );
};
export default Details;
