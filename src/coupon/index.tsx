/**
* @file index
* @date 2025-03-14
* <AUTHOR>
*/

import React, { useEffect, useState } from 'react';
import { queryCouponList } from '@/api/query';
import { log } from '@ali/iec-dtao-utils';
import { Breadcrumb, Drawer, Tab } from '@alifd/next';
import CouponActivityItem from '@/components/CouponActivityItem';
import CouponNormalItem from '@/components/CouponNormalItem';
import dayjs from 'dayjs';
import { money_US } from '@ali/iec-dtao-utils/esm/common/number';
import styles from './index.module.scss';
import CouponDrawer from '@/components/CouponDrawer';
import NoticePanel from '@/components/NoticePanel';
import { ENoticeType } from '@ali/mkt-design';
import { renderPrice } from '@/utils/tools';
import { getUrlParams } from '@/utils/params';

const COUPON_NAME = {
  PROMOTION_SUBSIDY: '活动优惠',
  BIZ_SUBSIDY: '行业优惠',
  PLATFORM_SUBSIDY: '平台优惠',
};

const TIP_CONTENT = {
  USING: '服务出单扣费时，系统将默认为您在生效且符合使用条件的权益中，选择最优的单项或叠加权益进行核销',
  UNAVAILABLE: '针对在权益有效期内创建并符合对应使用条件、且在权益失效后30天内发货的订单，出单扣费时仍可享受对应优惠；当前为您展示最近50个过期的权益，若有疑问请咨询平台客服',
};

const TAB_LIST = [{
  key: 'USING',
  title: '生效中',
}, {
  key: 'AVAILABLE',
  title: '待生效',
}, {
  key: 'UNAVAILABLE',
  title: '已失效',
}];

const renderLimitText = (item) => {
  const text: string[] = [];
  if (item?.totalPromotionQuota?.value) {
    text.push(`额度${money_US(item?.totalPromotionQuota?.value) || '-'}元`);
  }
  if (item?.maxUseTimes) {
    text.push(`限${item.maxUseTimes}次`);
  }
  if (text?.length) {
    return text.join(' ');
  }
  return null;
};

const renderTitle = (title) => {
  if (title && title.length > 15) {
    return `${title.slice(0, 15)}...`;
  }
  return title;
};

const Coupon = () => {
  const [couponList, setCouponList] = useState<[]>([]);
  const [isShowCouponDetail, setIsShowCouponDetail] = useState(false);
  const [currentId, setCurrentId] = useState(null);
  const [tipData, setTipData] = useState<Array<{ type: string; content: string }> | []>([]);

  const handleOnQueryCouponList = async ({ useStatus }) => {
    try {
      const couponListData = await queryCouponList({
        useStatus,
      });
      const { responseCode, merchantCouponDetailList } = couponListData;
      if (responseCode !== 'SUCCESS') {
        log.addLog('query-coupon-list', 'error', { responseCode });
        setCouponList([]);
        setTipData([]);
        return;
      }
      const data = merchantCouponDetailList?.map((item) => {
        const itemData = {
          id: item.id,
          label: COUPON_NAME[item.subsidyType],
          price: renderPrice(item),
          title: renderTitle(item.title),
          dateRange: item?.effectTime && item?.expireTime ? `${dayjs(item.effectTime).format('YYYY.MM.DD HH:mm')}～${dayjs(item.expireTime).format('YYYY.MM.DD HH:mm')}` : '-',
          limit: renderLimitText(item),
          rule: item.conditionDescription,
          status: item.customStatus,
          subsidyType: item?.subsidyType,
          hidden: item?.hidden,
        };
        return itemData;
      });
      setCouponList(data);
      const contentData = TIP_CONTENT[useStatus] && data?.length > 0 ? TIP_CONTENT[useStatus] : null;
      const tipItem = contentData ? [{ type: ENoticeType.notice, content: contentData }] : [];
      setTipData(tipItem);
    } catch (error) {
      log.addLog('query-coupon-list', 'error', { catch: error?.message });
      setCouponList([]);
      setTipData([]);
    }
  };

  useEffect(() => {
    const traceLog = getUrlParams(window.location.href, 'traceLog');
    log.addLog('byf-coupon-from', 'success', { from: traceLog ?? null });
    handleOnQueryCouponList({ useStatus: 'USING' });
  }, []);

  const onChange = (type) => {
    handleOnQueryCouponList({ useStatus: type });
  };

  const CouponItem = ({ data, type }) => {
    switch (type) {
      case 'PLATFORM_SUBSIDY':
        return <CouponNormalItem data={data} onClick={(d) => handleOnClick(d)} />;
      case 'BIZ_SUBSIDY':
      case 'PROMOTION_SUBSIDY':
        return <CouponActivityItem data={data} onClick={(d) => handleOnClick(d)} />;
      default:
        return <CouponNormalItem data={data} onClick={(d) => handleOnClick(d)} />;
    }
  };

  const handleOnClick = (id) => {
    setCurrentId(id);
    setIsShowCouponDetail(true);
  };


  const renderListPanel = (list) => {
    if (!list?.length) {
      return (
        <div className={styles.listPanel}>
          <div className={styles.noDataImg} />
          <div className={styles.noDataTitle}>暂无权益</div>
        </div>
      );
    }
    return list?.map((item) => {
      if (item?.hidden) {
        return null;
      }
      return <CouponItem data={item} type={item?.subsidyType} />;
    });
  };

  return (
    <div className={styles.coupon}>
      <div className={styles.couponBreadcrumb}>
        <Breadcrumb>
          <Breadcrumb.Item link="/home.htm/assets-byf-service/manage">退货宝</Breadcrumb.Item>
          <Breadcrumb.Item >我的权益</Breadcrumb.Item>
        </Breadcrumb>
      </div>
      <Tab triggerType="click" onChange={onChange}>
        {TAB_LIST.map((item) => (
          <Tab.Item
            key={item.key}
            title={item.title}
          />
        ))}
      </Tab>
      <NoticePanel data={tipData} />
      <div className={styles.couponListPanel}>
        {renderListPanel(couponList)}
      </div>
      <Drawer
        title="优惠券详情"
        placement="right"
        visible={isShowCouponDetail}
        width={800}
        onClose={() => {
          setIsShowCouponDetail(false);
          setCurrentId(null);
        }}
      >
        <CouponDrawer currentId={currentId} />
      </Drawer>
    </div>
  );
};

export default Coupon;
