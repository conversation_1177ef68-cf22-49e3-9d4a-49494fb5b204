import { BASE_URL, PRODUCT_CODE } from '@/constant';
import { QueryDeliveryRequest, QueryDeliveryResponse, QueryMerchantPromotionInfoResponse } from '@/types';
import { get } from '@/utils/ajax';

// 查询营销文案
export const queryDelivery = (data: QueryDeliveryRequest): Promise<QueryDeliveryResponse> => {
  return get({
    urlStr: `${BASE_URL}/curl/${PRODUCT_CODE}/deliveryHttp_queryDelivery`,
    data,
  });
};

// 查询大促活动
export const queryMerchantPromotionInfo = (): Promise<QueryMerchantPromotionInfoResponse> => {
  return get({
    urlStr: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantQueryService_queryMerchantPromotionInfo`,
  });
};
