import { BASE_URL, PRODUCT_CODE } from '@/constant';
import { AlipayInfoRequest, AlipayInfoResponse, ClaimOrderListRequest, ClaimOrderListResponse, CouponByIdRequest, CouponByIdResponse, CouponListRequest, CouponListResponse, DataIndicatorListResponse, MerchantServiceStatusResponse, QueryMerchantArrearsResponse, ServiceOrderInfoRequest, ServiceOrderInfoResponse } from '@/types';
import { get, post } from '@/utils/ajax';

// 查询用户服务状态
export const queryMerchantStatus = (): Promise<MerchantServiceStatusResponse> => {
  return get({
    urlStr: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantQueryService_queryMerchantStatus`,
  });
};

// 查询支付宝信息
export const queryAlipayInfo = (data: AlipayInfoRequest): Promise<AlipayInfoResponse> => {
  return get({
    urlStr: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantQueryService_queryAlipayInfo`,
    data,
  });
};

// 查询欠款数据
export const queryMerchantArrears = (): Promise<QueryMerchantArrearsResponse> => {
  return get({
    urlStr: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantQueryService_queryMerchantArrears`,
  });
};

// 查询服务单（含商品信息）
export const queryServiceOrderInfo = (data: ServiceOrderInfoRequest): Promise<ServiceOrderInfoResponse> => {
  return get({
    urlStr: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantQueryService_queryServiceOrderInfo`,
    data,
  });
};

// 查询补贴单列表
export const queryClaimOrderList = (data: ClaimOrderListRequest): Promise<ClaimOrderListResponse> => {
  return post({
    urlStr: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantQueryService_queryClaimOrderList`,
    data,
  });
};

// 查询券列表
export const queryCouponList = (data: CouponListRequest): Promise<CouponListResponse> => {
  return post({
    urlStr: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantQueryService_queryCouponList`,
    data,
  });
};

// 查询券详情
export const queryCouponById = (data: CouponByIdRequest): Promise<CouponByIdResponse> => {
  return post({
    urlStr: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantQueryService_queryCouponById`,
    data,
  });
};

// 管理指标查询
export const queryDataIndicatorList = (): Promise<DataIndicatorListResponse> => {
  return get({
    urlStr: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantQueryService_queryDataIndicatorList`,
  });
};
