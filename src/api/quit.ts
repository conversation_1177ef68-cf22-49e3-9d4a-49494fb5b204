import { BASE_URL, PRODUCT_CODE } from '@/constant';
import { BaseQuitRequest, BaseQuitResponse } from '@/types';
import { post } from '@/utils/ajax';

interface BaseResponse {
  success?: boolean;
  responseCode: string;
  responseMessage?: string;
}

enum QuitRetainPopTypeEnum {
  forward = 'forward',
  backward = 'backward',
}

interface QuitAdmitRequest {
  channel: string;
}

interface QuitAdmitResponse extends BaseResponse {
  responseCode: string;
  isAdmit: boolean;
  attributes: {
    highRiskRefund: boolean;
    fixedPriceType: string;
    quitRetainPopupType: QuitRetainPopTypeEnum;
    rejectRuleList: Array<{
      name: string;
      scene: string;
      beginTime: number;
      endTime: number;
      status: string;
    }>;
  };
}

export interface ServiceFeeDetails {
  feeAmount: {
    value: string;
  };
  channelCode: string;
  simulateRuleId: string;
  exemptionAmount: string;
  originalFeeAmount: {
    value: string;
  };
  attributes: { trialWithChannelCode: boolean };
}

interface QueryBatchTrailResponse extends BaseResponse {
  canSendCoupon: boolean;
  attributes: {
    effectDays: string;
    discount: string;
    effectiveDays: string;
    showDiscount: string;
    canSendCoupon: boolean;
    canNotQuitDays: string;
  };
  serviceFeeDetails: {
    currentServiceFee: ServiceFeeDetails;
    discountServiceFee: ServiceFeeDetails;
  };
}
interface QueryBatchTrailRequest {
  channel: string;
}

interface ReceiveRedEnvelopeRequest {
  identifier: string;
  expectDiscount: string;
  expectShowDiscount: string;
  expectEffectiveDays: string;
}

interface ReceiveRedEnvelopeResponse extends BaseResponse {
  sendCoupon: boolean;
  attributes: {
    errorCode: string;
  };
}

// 用户退出
export const baseQuit = (data: BaseQuitRequest): Promise<BaseQuitResponse> => {
  return post({
    urlStr: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantQuitService_baseQuit`,
    data,
  });
};

// 退出准入
export const quitAdmit = (data: QuitAdmitRequest): Promise<QuitAdmitResponse> => {
  return post({
    urlStr: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantQuitService_quitAdmit`,
    data,
  });
};

// 试算信息
export const queryBatchTrail = (data: QueryBatchTrailRequest): Promise<QueryBatchTrailResponse> => {
  return post({
    urlStr: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantQuitService_queryBatchTrail`,
    data,
  });
};

// 领取红包
export const receiveRedEnvelope = (data: ReceiveRedEnvelopeRequest): Promise<ReceiveRedEnvelopeResponse> => {
  return post({
    urlStr: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantQuitService_receiveRedEnvelope`,
    data,
  });
};
