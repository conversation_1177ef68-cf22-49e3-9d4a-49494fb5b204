import { BASE_URL, PRODUCT_CODE } from '@/constant';
import {
  BaseAdmitResponse,
  BaseAndFixedOpenRequest,
  BaseOpenProps,
  BaseOpenResponse,
  BaseTrailResponse,
  FixedAdmitAndTrailResponse,
  FixedAdmitResponse,
  FixedOpenRequest,
  FixedOpenResponse,
  FixedRenewalAdmitResponse,
  FixedTrailRequest,
  FixedTrailResponse,
  GiveAdmitRequest,
  GiveAdmitResponse,
  PartSettingRequest,
  PartSettingResponse,
} from '@/types';
import { get, post } from '@/utils/ajax';

// 查询日常准入&试算
export const baseAdmitAndTrailInfo = (): Promise<BaseAdmitResponse> => {
  return get({
    urlStr: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_baseAdmitAndTrailInfo`,
  });
};

// 查询框类准入&试算
export const batchAdmitFixedPrice = (): Promise<any> => {
  return get({
    urlStr: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_batchAdmitFixedPrice`,
  });
};

// 框类单独试算
export const trailFixedPrice = (data): Promise<any> => {
  return get({
    urlStr: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_trailFixedPrice`,
    data,
  });
};

// 查询固定价准入&试算
export const fixedAdmitAndTrailInfo = (): Promise<FixedAdmitAndTrailResponse> => {
  return get({
    urlStr: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_fixedAdmitAndTrailInfo`,
  });
};

// 查询日常准入
export const baseAdmit = (): Promise<BaseAdmitResponse> => {
  return get({
    urlStr: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_baseAdmit`,
  });
};

// 查询固定价准入
export const fixedAdmit = (data): Promise<FixedAdmitResponse> => {
  return get({
    urlStr: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_fixedAdmit`,
    data,
  });
};

// 查询年框续约准入
export const renewalBatchAdmitFixedPrice = (data): Promise<FixedRenewalAdmitResponse> => {
  return get({
    urlStr: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_renewalBatchAdmitFixedPrice`,
    data,
  });
};

// 查询日常试算
export const baseTrail = (): Promise<BaseTrailResponse> => {
  return get({
    urlStr: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_baseTrail`,
  });
};

// 查询固定价试算
export const fixedTrail = (data: FixedTrailRequest): Promise<FixedTrailResponse> => {
  return get({
    urlStr: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_fixedTrail`,
    data,
  });
};

// 查询框类续约试算（已开）
export const renewalTrailFixedPrice = (data: FixedTrailRequest): Promise<FixedTrailResponse> => {
  return get({
    urlStr: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_renewalTrailFixedPrice`,
    data,
  });
};

// 查询框类续约试算（未开）
export const renewalBatchTrailFixedPrice = (data: FixedTrailRequest): Promise<any> => {
  return get({
    urlStr: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_renewalBatchTrailFixedPrice`,
    data,
  });
};

// 日常开通
export const baseOpen = (data: BaseOpenProps): Promise<BaseOpenResponse> => {
  return post({
    urlStr: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_baseOpen`,
    data,
  });
};

// 固定价开通
export const fixedOpen = (data: FixedOpenRequest): Promise<FixedOpenResponse> => {
  return post({
    urlStr: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_fixedOpen`,
    data,
  });
};

// 日常固定价直接开
export const baseAndFixedOpen = (data: BaseAndFixedOpenRequest): Promise<BaseOpenResponse> => {
  return post({
    urlStr: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_baseAndFixedOpen`,
    data,
  });
};

// 年框续约开通
export const fixedRenewalOpen = (data: FixedOpenRequest): Promise<FixedOpenResponse> => {
  return post({
    urlStr: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_fixedRenewalOpen`,
    data,
  });
};

// 赠送准入
export const giveAdmit = (data: GiveAdmitRequest): Promise<GiveAdmitResponse> => {
  return get({
    urlStr: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_giveAdmit`,
    data,
  });
};

// 服务设置
export const changePartSetting = (data: PartSettingRequest): Promise<PartSettingResponse> => {
  return post({
    urlStr: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_changePartSetting`,
    data,
  });
};
