import React, { useEffect } from 'react';
import { Loading } from '@alifd/next';
import { queryMerchantStatus } from '@/api/query';
import styles from './index.module.scss';
import { getUrlParams } from '@/utils/params';
import { log } from '@ali/iec-dtao-utils';
import { queryMerchantPromotionInfo } from '@/api/promotion';

const OPEN_URL = '/open';
const MANAGE_URL = '/manage';
const PROMOTION_URL = '/promotion';

const Routing = (props) => {
  useEffect(() => {
    const traceLog = getUrlParams(window.location.href, 'traceLog');
    log.addLog('byf-from', 'success', { from: traceLog ?? null });
    handleOnGetStatus(traceLog);
  }, []);

  const handleOnGetStatus = async (traceLog) => {
    const openUrl = traceLog ? `${OPEN_URL}?traceLog=${traceLog}` : OPEN_URL;
    const manageUrl = traceLog ? `${MANAGE_URL}?traceLog=${traceLog}` : MANAGE_URL;
    const promotionUrl = traceLog ? `${MANAGE_URL}?traceLog=${traceLog}` : PROMOTION_URL;
    try {
      const statusData = await queryMerchantStatus();
      const activitiesData = await queryMerchantPromotionInfo();
      let redirectUrl = openUrl;
      switch (statusData?.status) {
        case 'null':
        case 'MERCHANT_OPEN_FAILED':
        case 'MERCHANT_QUIT_SUCCEED':
          redirectUrl = activitiesData?.open ? promotionUrl : openUrl;
          break;
        case 'MERCHANT_OPENING':
          redirectUrl = statusData?.openChannel === 'qianniu_pc_promotion' ? promotionUrl : openUrl;
          break;
        case 'MERCHANT_OPEN_SUCCEED':
        case 'MERCHANT_FROZEN':
        case 'MERCHANT_QUITTING':
          redirectUrl = manageUrl;
          break;
        default:
          redirectUrl = openUrl;
          break;
      }
      props?.history.push(redirectUrl);
    } catch (error) {
      log.addLog('route-query-status', 'error', { catch: error?.message });
      props?.history.push(openUrl);
    }
  };

  return (
    <div className={styles.routing}>
      <Loading />
    </div>
  );
};
export default Routing;
