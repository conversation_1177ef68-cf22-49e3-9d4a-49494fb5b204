import { useEffect, useCallback, useState, useMemo } from 'react';
import { includes } from 'lodash-es';
import { ENoticeType } from '@ali/mkt-design';

const STATUS_TIPS_INFO = {
  MERCHANT_FROZEN: {
    type: ENoticeType.error,
    content: '系统识别您的店铺异常，暂时冻结退货宝服务，为此给您带来的不便敬请谅解。请保障店铺正常经营，如后续异常解除，平台会自动解除冻结',
  },
  MERCHANT_FROZEN_ARREARS: {
    type: ENoticeType.error,
    content: '您的店铺已欠费，暂时关闭退货宝服务，请尽快向服务费扣款支付宝账户充值，充值后系统将定期从您的账户中自动划扣，扣费成功将自动解除冻结',
  },
  MERCHANT_FROZEN_OTHER: {
    type: ENoticeType.error,
    content: '系统识别您的店铺异常，暂时冻结退货宝服务，为此给您带来的不便敬请谅解。请保障店铺正常经营，如后续异常解除，平台会自动解除冻结',
  },
  MERCHANT_QUITTING: {
    type: ENoticeType.warn,
    content: '服务退出中，请耐心等待。',
  },
};

const OPEN_TIPS = {
  type: ENoticeType.notice,
  content: '服务费价格调整为每日更新，请及时关注服务费价格变化',
};

interface TipItem {
  type: ENoticeType;
  content: string;
}

const useTips = ({
  status,
  isOpenFixed,
  customerFreezeCode,
}) => {
  const [tips, setTips] = useState<TipItem[]>([]);

  const handleOnGetTips = useCallback(() => {
    const newTips: TipItem[] = [];

    // 冻结
    if (includes(['MERCHANT_FROZEN'], status)) {
      // 考虑兼容发布情况
      if (customerFreezeCode) {
        newTips.push(STATUS_TIPS_INFO[`${status}_${customerFreezeCode}`] || STATUS_TIPS_INFO[status]);
      } else {
        newTips.push(STATUS_TIPS_INFO[status]);
      }
    } else if (includes(['MERCHANT_QUITTING'], status)) { // 退出中
      newTips.push(STATUS_TIPS_INFO[status]);
    } else if (status === 'MERCHANT_OPEN_SUCCEED' && !isOpenFixed) { // 开通成功&非年框用户
      newTips.push(OPEN_TIPS);
    }
    setTips(newTips);
  }, [status, isOpenFixed, customerFreezeCode]);

  useEffect(() => {
    handleOnGetTips();
  }, [handleOnGetTips]);

  return useMemo(() => tips, [tips]);
};

export default useTips;
