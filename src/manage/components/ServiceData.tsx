import React from 'react';
import dayjs from 'dayjs';
import numeral from 'numeral';
import styles from './styles.module.scss';
import { money_US } from '@ali/iec-dtao-utils/esm/common/number';

const ServiceData = ({ indicatorData, dataUpdateDeadline }) => {
  if (!indicatorData?.length) {
    return <></>;
  }

  const renderIndictor = (value, unit) => {
    if (unit === '元' && value) {
      return money_US(value);
    }
    if (value) {
      return numeral(value).format('0,0');
    }
    return '--';
  };

  return (
    <div className={styles['service-data']}>
      <div className={styles['service-data-title']}>
        服务数据
        {
          dataUpdateDeadline ? <span>截止{dayjs(dataUpdateDeadline).format('YYYY-MM-DD HH:mm:ss')}</span> : <></>
        }
      </div>
      <div className={styles['service-data-panel']}>
        {
          indicatorData?.map((item) => {
            return (
              <div className={styles['service-data-panel-info']}>
                <div className={styles['service-data-panel-info-key']}>
                  {item?.title}
                  {!item?.value && <div className={styles['service-data-panel-info-key-tag']}>待更新</div>}
                </div>
                <div className={styles['service-data-panel-info-val']}>
                  {renderIndictor(item?.value, item?.unit)}
                  {
                    item?.unit && <span>{item?.unit}</span>
                  }
                </div>
              </div>
            );
          })
        }
      </div>
    </div>
  );
};

export default ServiceData;
