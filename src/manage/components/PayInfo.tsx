import React from 'react';
import styles from './styles.module.scss';
import { Balloon } from '@alifd/next';

const PayInfo = ({ alipayLoginId }: { alipayLoginId: string }) => {
  return (
    <div className={styles['pay-info']}>
      服务费扣款账户：
      <div className={styles['pay-alipay-icon']} />
      {alipayLoginId || '--'}
      <Balloon
        v2
        trigger={<div className={styles['pay-normal-icon']} />}
        align="r"
        triggerType="hover"
        closable={false}
      >
        <div>请确保账户资金充足，避免服务失效，从而导致客诉</div>
      </Balloon>
    </div>
  );
};
export default PayInfo;
