import React from 'react';
import styles from './styles.module.scss';
import ServiceFeeArrears from '@/components/ServiceFeeArrears';
import { money_US } from '@ali/iec-dtao-utils/esm/common/number';
import dayjs from 'dayjs';
import PayInfo from './PayInfo';
import { includes } from 'lodash-es';
import { BELL_ICON, PRICE_RENDER_CONFIG, UNQUIET_STATUS } from '@/constant';
import CommonTag from '@/components/CommonTag';
import { Button, Icon } from '@alifd/next';
import { log } from '@ali/iec-dtao-utils';
import BalloonContent from '@/components/BalloonContent';

const FixedFeePanel = ({
  status,
  fixedPriceType,
  fixedFeeAmount,
  fixedDiscountFeeAmount,
  fixedPromotionFlag,
  effectTime,
  isArrears,
  expireTime,
  alipayLoginId,
  currFixedPriceExpireTime,
  currFixedPriceExpireSoon,
  currFixedPriceRenewal,
  isFixedRenewalAdmit,
  fixedRenewalData,
  onRenew,
}) => {
  // 固定价版命中退出中、冻结中
  if (includes(UNQUIET_STATUS, status)) {
    return (
      <div className={styles['fixed-fee']}>
        <PayInfo alipayLoginId={alipayLoginId} />
      </div>
    );
  }

  // 框类续签提示
  const renderFixedRenewal = () => {
    // 框类即将到期&未续签&续签准入通过
    if (currFixedPriceExpireSoon && !currFixedPriceRenewal && isFixedRenewalAdmit) {
      log.addLog('fixed-price-expire-tip-show', 'success');
      return (
        <div className={styles['fixed-fee-renew']} style={{ border: '1px solid #E2E7FF', background: ' linear-gradient(90deg, #F6F8FF 2%, #F5F7FF 100%)' }}>
          <div>
            <div className={styles['fixed-fee-renew-title']}>
              <span className={styles['fixed-fee-renew-text']}>{PRICE_RENDER_CONFIG[fixedPriceType]?.title}续签提醒</span>
              <div className={styles['fixed-fee-renew-tip']} style={{ background: 'linear-gradient(90deg, #CDD6FF 0%, rgba(238, 241, 255, 0.45) 100%)' }}>
                <img src={BELL_ICON} />
                <span>{PRICE_RENDER_CONFIG[fixedPriceType]?.title}{currFixedPriceExpireTime ? dayjs(currFixedPriceExpireTime).format('YYYY年MM月DD日') : '--'}到期后将恢复日常浮动定价</span>
              </div>
            </div>
            <div className={styles['fixed-fee-renew-content']}>
              <div className={styles['fixed-fee-renew-content-text']}>已为您<span>优先开放</span>续签资格，可<span>提前锁定</span>一口价</div>
            </div>
          </div>
          <Button type="primary" onClick={() => onRenew()} className={styles['fixed-fee-renew-btn']}>去续签 <Icon type="arrow-right" /></Button>
        </div>
      );
    }
    // 框类即将到期&已签约
    if (currFixedPriceExpireSoon && currFixedPriceRenewal) {
      log.addLog('fixed-price-signed-tip-show', 'success');
      return (
        <div className={styles['fixed-fee-renew']} style={{ border: '1px solid #E7F4E7', background: 'linear-gradient(90deg, #F7FDF7 2%, #F9FFF9 100%)' }}>
          <div>
            <div className={styles['fixed-fee-renew-title']}>
              <span className={styles['fixed-fee-renew-text']}>{PRICE_RENDER_CONFIG[fixedPriceType]?.title}续签成功</span>
              <div className={styles['fixed-fee-renew-tip']} style={{ background: '#fff' }}>
                <img src={BELL_ICON} />
                <span>恭喜您提前锁定，续签的{PRICE_RENDER_CONFIG[fixedPriceType]?.title}将于{fixedRenewalData?.effectTime ? dayjs(fixedRenewalData?.effectTime).format('YYYY年MM月DD日') : '--'}生效</span>
              </div>
            </div>
            <div className={styles['fixed-fee-renew-content']}>
              <div className={styles['fixed-fee-renew-content-amount']}>
                {
                  fixedRenewalData?.promotionFlag ? (
                    <>
                      <span className={styles['fixed-fee-renew-amount']}>¥{money_US(fixedRenewalData?.discountFeeAmount)}</span>
                      <span className={styles['fixed-fee-renew-unit']}> /单</span>
                      <span className={styles['fixed-fee-renew-origin']}>¥{money_US(fixedRenewalData?.originalFeeAmount)} /单</span>
                      <BalloonContent
                        icon={<Icon type="help" style={{ color: '#999', marginRight: 4 }} />}
                        align="tl"
                        contentWidth={120}
                        contentChildren={'该价格为优惠前的价格'}
                      />
                    </>
                  ) : (
                    <>
                      <span className={styles['fixed-fee-renew-amount']}>¥{money_US(fixedRenewalData?.originalFeeAmount)}</span>
                      <span className={styles['fixed-fee-renew-unit']}> /单</span>
                    </>
                  )}
                <span>(有效期：{fixedRenewalData?.effectTime ? dayjs(fixedRenewalData?.effectTime).format('YYYY-MM-DD') : '--'} ~ {fixedRenewalData?.expireTime ? dayjs(fixedRenewalData?.expireTime).format('YYYY-MM-DD') : '--'})</span>
              </div>
            </div>
          </div>
        </div>
      );
    }
    return <></>;
  };

  return (
    <>
      {/* 框类续签 */}
      {renderFixedRenewal()}
      <div className={styles.fixed}>
        <div className={styles['fixed-fee']}>
          <div className={styles['fixed-fee-title']}>
            当前服务费
          </div>
          <div className={styles['fixed-fee-content']}>
            {
              fixedPromotionFlag ? (
                <>
                  <span className={styles['fixed-fee-content-title-amount']}>¥{money_US(fixedDiscountFeeAmount ?? '-')}</span>
                  <span className={styles['fixed-fee-content-title-unit']}> /单</span>
                  <span className={styles['fixed-fee-content-title-origin']}>¥{money_US(fixedFeeAmount ?? '-')} /单</span>
                  <span>
                    <BalloonContent
                      icon={<Icon type="help" style={{ color: '#999', marginRight: 4 }} />}
                      align="tl"
                      contentWidth={120}
                      contentChildren={'该价格为优惠前的价格'}
                    />
                  </span>
                </>
              ) : (
                <>
                  <span className={styles['fixed-fee-content-title-amount']}>¥{money_US(fixedFeeAmount ?? '-')}</span>
                  <span className={styles['fixed-fee-content-title-unit']}> /单</span>
                </>
              )
            }
            <CommonTag bgcolor={'rgba(255, 128, 0, 0.06)'} color={'#FF8000'} text={`${PRICE_RENDER_CONFIG[fixedPriceType]?.title}`} />
          </div>
          <div className={styles['fixed-fee-info']}>
            <div className={styles['fixed-fee-content-stage']}>
              有效期：{effectTime ? dayjs(effectTime).format('YYYY-MM-DD') : '-'} ~ {expireTime ? dayjs(expireTime).format('YYYY-MM-DD') : '-'}
            </div>
            <PayInfo alipayLoginId={alipayLoginId} />
          </div>
        </div>
        <ServiceFeeArrears isArrears={isArrears} className={styles['fixed-fee-arrears']} logKey="fixed-fee-arrears-btn" />
      </div>
    </>
  );
};
export default FixedFeePanel;
