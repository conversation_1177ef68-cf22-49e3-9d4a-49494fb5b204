export const MERCHANT_OPENING = 'MERCHANT_OPENING';
export const MERCHANT_OPEN_SUCCEED = 'MERCHANT_OPEN_SUCCEED';
export const MERCHANT_OPEN_FAILED = 'MERCHANT_OPEN_FAILED';
export const MERCHANT_FROZEN = 'MERCHANT_FROZEN';
export const MERCHANT_QUITTING = 'MERCHANT_QUITTING';
export const MERCHANT_QUIT_SUCCEED = 'MERCHANT_QUIT_SUCCEED';
export const MERCHANT_NOT_OPEN = 'null';
export const BASE_URL = 'https://b2bfin.taobao.com';
export const PRODUCT_CODE = 'BYF';
export const NO_ADMIT_IMG = 'https://img.alicdn.com/imgextra/i4/O1CN01Qm42M91jgJlZjwACl_!!*************-2-tps-360-300.png';
export const TRADE_ACCOUNT = 'TRADE_ACCOUNT';

export const STATUS_INFO = {
  null: {
    title: '暂未开通',
    color: '#31CC31',
    bgColor: 'rgba(49, 204, 49, 0.06)',
  },
  MERCHANT_OPENING: {
    title: '开通中',
    color: '#31CC31',
    bgColor: 'rgba(49, 204, 49, 0.06)',
  },
  MERCHANT_OPEN_SUCCEED: {
    title: '保障中',
    color: '#31CC31',
    bgColor: 'rgba(49, 204, 49, 0.06)',
  },
  MERCHANT_OPEN_FAILED: {
    title: '暂未开通',
    color: '#31CC31',
    bgColor: 'rgba(49, 204, 49, 0.06)',
  },
  MERCHANT_FROZEN: {
    title: '已冻结',
    color: '#FF0000',
    bgColor: 'rgba(255, 0, 0, 0.06)',
  },
  MERCHANT_QUITTING: {
    title: '退出中',
    color: '#FF8000',
    bgColor: 'rgba(255, 128, 0, 0.06)',
  },
  MERCHANT_QUIT_SUCCEED: {
    title: '暂未开通',
    color: '#31CC31',
    bgColor: 'rgba(49, 204, 49, 0.06)',
  },
};

export const SERVICE_STATUS_INFO = {
  INIT: {
    bgColor: 'rgba(61, 94, 255, 0.06)',
    color: '#3D5EFF',
    title: '待生效',
  },
  GUARANTEED: {
    bgColor: 'rgba(49, 204, 49, 0.06)',
    color: '#31CC31',
    title: '保障中',
  },
  EXPIRED: {
    bgColor: '#F7F8FA',
    color: '#999999',
    title: '已失效',
  },
  null: {
    bgColor: '#F7F8FA',
    color: '#999999',
    title: '-',
  },
};


export const CLAIM_STATUS_INFO = {
  PAY_WAITING: {
    bgColor: 'rgba(61, 94, 255, 0.06)',
    color: '#3D5EFF',
    title: '补偿中',
  },
  SUCCEEDED: {
    bgColor: 'rgba(49, 204, 49, 0.06)',
    color: '#31CC31',
    title: '补偿成功',
  },
  FAILED: {
    color: '#FF0000',
    bgColor: 'rgba(255, 0, 0, 0.06)',
    title: '补偿失败',
  },
  null: {
    color: '#FF0000',
    bgColor: 'rgba(255, 0, 0, 0.06)',
    title: '-',
  },
};

export const FIXED_TAG = {
  YEAR: '年框',
  HALF_YEAR: '半年框',
  SEASON: '季框',
};

export const QUIT_REASON_LIST = [
  {
    value: '成交转化没有提升',
    label: '成交转化没有提升',
    desc: '含退货宝服务的商品，平均订单成交转化提升了约25%。',
  }, {
    value: '店铺纠纷下降不及预期',
    label: '店铺纠纷下降不及预期',
    desc: '含退货宝服务订单的投诉率降低了约40%，避免运费纠纷。',
  },
  {
    value: '服务费太高，支出成本大',
    label: '服务费太高，支出成本大',
    desc: '服务费和您的退货率相关，您接受的服务费价格是:',
    isInput: true,

  },
  {
    value: '赔付金额过高，消费者恶意骗补',
    label: '赔付金额过高，消费者恶意骗补',
    desc: '我们正在持续治理买家恶意骗补行为，若您发现此情况，您可以通过万象客服进行反馈，我们将第一时间核实处理。',
  },
  {
    value: '赔付金额过低，买家要求店铺补运费差价',
    label: '赔付金额过低，买家要求店铺补运费差价',
  },
  {
    value: '商品退货率明显上升',
    label: '商品退货率明显上升',
  },
  {
    value: '其他',
    label: '其他',
    isInput: true,
  },
];
export const UNQUIET_STATUS = ['MERCHANT_FROZEN', 'MERCHANT_QUITTING'];
export const WARNING_INFO_IMG = 'https://img.alicdn.com/imgextra/i1/O1CN01pPCarG1bmFygGJztb_!!6000000003507-2-tps-36-36.png';

export const BAXIA_REGEX = /byfMerchantOpenService_baseOpen/i;

export const QUIT_TYPE = 'SELLER_QUIT';

export const PAYEE_TYPE = {
  BUYER: '买家',
  LOGISTICS_PARTY: '物流部',
};
export const PAYEE_TYPE_STATUS = {
  BUYER: '补偿成功',
  LOGISTICS_PARTY: '上门取件成功 ，已自动减免首重费用',
};

export const HEADER_LOGO = 'https://img.alicdn.com/imgextra/i1/O1CN01jaaO8L1gaPpoJLDVu_!!6000000004158-2-tps-984-48.png';

export const PROTOCOL_URL = 'https://terms.alicdn.com/legal-agreement/terms/b_end_product_protocol/*****************/*****************.html';

export const NO_CLAIMS_DATA_IMG = 'https://img.alicdn.com/imgextra/i2/O1CN019omHXu1KzDiqiPbts_!!*************-2-tps-160-134.png';

export const HOME_BANNER_POSITION_CODE = 'byfHomeTopBanner';

export const HOME_NOTICE_POSITION_CODE = 'byfHomeMidNotice';

export const OPEN_FAILED_CODE = {
  USER_HAS_BLOCK: '您的支付宝账号被冻结，请先到支付宝解冻后再开通',
  ACCOUNT_LEVEL_CHECK_FAIL: '您的支付宝账号认证等级不足，请先到支付宝完成认证后再开通',
  STATUS_NO_CERT: '您的支付宝账号暂未实名，请先到支付宝完成实名后再开通',
  LOGONID_IS_REPEATED: '您的支付宝账号与其他人重复，暂无法成功开通，如有疑问请联系客服',
  ADMIT_REJECT: '很抱歉，您暂不满足退货宝的开通条件，如有疑问请联系客服',
  DEFAULT_FRONT_ERROR: '开通失败，请联系客服',
};

export const MARKET_TIPS_CONTENT = [
  '优衣**店、有棵**店、 叽咕**装、巴拉**店等超80%商家已加入！',
  '宝洁**店、维达**店、十月**店、欧莱**店等超80%商家已加入！',
  '安踏**店、李宁**店、FILA**店等超80%商家已加入！',
];

// 用增资源位
export const ByfOpenBtnMarketingTextPC = 'byfOpenBtnMarketingTextPC';
export const ByfHomeBtnMarketingTextPC = 'byfHomeBtnMarketingTextPC';

// 线下打款
export const OFFLINE_PAYMENT_URL = 'https://myseller.taobao.com/home.htm/whale-accountant/biz-inquiry?active=arrears';
// 服务欠费
export const SERVICE_FEE_ARREARS_TEXT = '欠费已超6小时，请确保支付宝余额充足以免影响服务保障';
// 开通页欠费
export const ACCOUNT_ARREARS_TEXT = '您的账户已欠费，请尽快缴纳欠款';
// 高退
export const HIGH_RISK_TEXT = '您店铺风险较高，本无法开通。为支持店铺经营，开放特殊开通通道，请确认费率后开通';
// 去还款
export const REDOUND_ARROW = 'https://img.alicdn.com/imgextra/i2/O1CN014g7dR01Fhka01gNqT_!!*************-2-tps-14-14.png';
export const QIANNIU_PC = 'qianniu_pc';

export const NO_COUPON_DATA = 'https://img.alicdn.com/imgextra/i2/O1CN01DaKvq91vQzyZT77W3_!!*************-2-tps-360-300.png';
// 红包赔付
export const COMPENSATION_RED_ENVELOPE = 'COMPENSATION_RED_ENVELOPE';

export const RED_ENVELOPE_DESC = '领取成功后可到「退货宝-我的权益」查看';

// export const RISE_IMG_URL = 'https://gw.alicdn.com/imgextra/i2/O1CN01KEtrxh1tHiixSiRSE_!!*************-2-tps-203-138.png';

export const RED_ENVELOPE_ICON = 'https://gw.alicdn.com/imgextra/i1/O1CN01jA4xjJ1e6WoDDMons_!!*************-2-tps-32-32.png';

export const INTEREST_LIST = [{
  text: '流量曝光',
  exposure: 26,
  path: 'https://gw.alicdn.com/imgextra/i1/O1CN01aApQJD1a9UujNMpq8_!!6000000003287-2-tps-168-153.png',
},
{
  text: '专属标识促进下单转化',
  exposure: 20,
  path: 'https://gw.alicdn.com/imgextra/i2/O1CN01pbsPkR1KGKyc3qlOG_!!6000000001136-2-tps-168-153.png',
}];

export const INTEREST_DISPUTE_INFO = {
  text: '纠纷发起率',
  exposure: 67,
  decPath: 'https://gw.alicdn.com/imgextra/i4/O1CN01PWvRXI1OS1XuSrw4r_!!6000000001703-2-tps-168-153.png',
  risePath: 'https://gw.alicdn.com/imgextra/i2/O1CN01KEtrxh1tHiixSiRSE_!!*************-2-tps-203-138.png',
};

export const RECEIVE_RESULT_SUCCESS_INFO = {
  iconPath: 'https://gw.alicdn.com/imgextra/i4/O1CN01SskjGh28t1DxqxMvG_!!6000000007989-2-tps-48-48.png',
  title: '领取成功',
  desc: '恭喜，您的权益已到账，保障持续不断档，退货宝将努力为您提供更好的服务',
  code: 'success',
};

export const RECEIVE_RESULT_ERROR_INFO = {
  'ERROR-2001': {
    iconPath: 'https://gw.alicdn.com/imgextra/i4/O1CN016H0vFO1N6fTFbOSAo_!!6000000001521-2-tps-48-48.png',
    title: '领取失败',
    desc: '抱歉，您已经参与过此活动，暂时无法领取，您是否仍选择退出？',
    code: 'ERROR-2001',
  },
  'ERROR-4017': {
    iconPath: 'https://gw.alicdn.com/imgextra/i4/O1CN016H0vFO1N6fTFbOSAo_!!6000000001521-2-tps-48-48.png',
    title: '领取失败',
    desc: '系统异常，暂时无法参与当前活动，请在后续保持关注，您是否仍选择退出？',
    code: 'ERROR-4017',
  },
  default: {
    iconPath: 'https://gw.alicdn.com/imgextra/i4/O1CN016H0vFO1N6fTFbOSAo_!!6000000001521-2-tps-48-48.png',
    title: '领取失败',
    desc: '系统异常，暂时无法参与当前活动，您是否仍选择退出？',
    code: 'default',
  },
};

export const BELL_ICON = 'https://gw.alicdn.com/imgextra/i1/O1CN019Tx3CD1bsDD88wlM5_!!6000000003520-2-tps-32-32.png';

export const RENEWAL_DIALOG_TIPS = '承诺在有效期内不中途退出，有效期内服务费不变';

export const renewalDialogStyle = { backgroundColor: 'rgba(61, 94, 255, 0.08)', fontSize: '12px', padding: '6px 12px' };

export const NO_QUIT_IMG = 'https://gw.alicdn.com/imgextra/i3/O1CN010twIt71DEJwPKUMzz_!!6000000000184-2-tps-240-36.png';

export const ACTIVITY_TYPE_CODE = {
  DAILY: 'DAILY',
  MONTH: 'MONTH',
  YEAR: 'YEAR',
};

export const PRICE_TYPE = {
  DAILY: 'DAILY',
  MONTH: 'MONTH',
  YEAR: 'YEAR',
};

export const PRICE_RENDER_CONFIG = {
  DAILY: {
    title: '今日服务费',
    subTitle: '服务费将根据店铺交易订单退换货等情况动态调整，最终以实际出单收费为准',
    desc: '价格动态更新',
  },
  MONTH: {
    title: '月框一口价',
    desc: '专属特权，30天固定价格',
  },
  YEAR: {
    title: '年框一口价',
    desc: '专属特权，365天固定价格',
  },
  default: {
    title: '--',
    subTitle: '--',
    desc: '--',
  },
};
